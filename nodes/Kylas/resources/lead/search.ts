import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadSearch = {
    operation: ['search'],
    resource: ['lead'],
};

export const leadSearchDescription: INodeProperties[] = [
    {
        displayName: "Additional Options",
        name: "additionalOptions",
        type: "collection",
        placeholder: "Add Option",
        default: {},
        displayOptions: {
            show: showOnlyForLeadSearch,
        },
        options: [
            {
                displayName: "Limit",
                name: "limit",
                type: "number",
                typeOptions: {
                    minValue: 1,
                },
                default: 50,
                description: "Max number of results to return",
                routing: {
                    send: {
                        type: 'query',
                        property: 'limit',
                    },
                },
            },
            {
                displayName: "Offset",
                name: "offset",
                type: "number",
                default: 0,
                description: "Number of results to skip",
                routing: {
                    send: {
                        type: 'query',
                        property: 'offset',
                    },
                },
            },
            {
                displayName: "Search Query",
                name: "q",
                type: "string",
                default: "",
                description: "Search query to filter leads",
                routing: {
                    send: {
                        type: 'query',
                        property: 'q',
                    },
                },
            },
            {
                displayName: "Sort By",
                name: "sortBy",
                type: "string",
                default: "",
                description: "Field to sort by",
                routing: {
                    send: {
                        type: 'query',
                        property: 'sortBy',
                    },
                },
            },
            {
                displayName: "Sort Order",
                name: "sortOrder",
                type: "options",
                options: [
                    { name: "Ascending", value: "asc" },
                    { name: "Descending", value: "desc" },
                ],
                default: "asc",
                routing: {
                    send: {
                        type: 'query',
                        property: 'sortOrder',
                    },
                },
            },
        ]
    }
];
