import type { INodeProperties } from 'n8n-workflow';
import { leadCreateDescription } from './create';
import { customFieldsDescription } from './customFields';

const showOnlyForLeads = {
	resource: ['lead'],
};

export const leadDescription: INodeProperties[] = [
	{
		displayName: 'Operation',
		name: 'operation',
		type: 'options',
		noDataExpression: true,
		displayOptions: {
			show: showOnlyForLeads,
		},
		options: [
			{
				name: 'Get Many',
				value: 'search',
				action: 'Get leads',
				description: 'Get many leads',
				routing: {
					request: {
						method: 'GET',
						url: '/leads',
					},
				},
			},
			{
				name: 'Get',
				value: 'get',
				action: 'Get a lead',
				description: 'Get the data of a single lead',
				routing: {
					request: {
						method: 'GET',
						url: '=/leads/{{$parameter.userId}}',
					},
				},
			},
			{
				name: 'Create',
				value: 'create',
				action: 'Create a new lead',
				description: 'Create a new lead',
				routing: {
					request: {
						method: 'POST',
						url: '/v1/leads',
					},
				},
			},
		],
		default: 'get',
	},
	...leadCreateDescription,
	...customFieldsDescription
];
