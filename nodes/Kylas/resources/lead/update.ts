import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadUpdate = {
    operation: ['update'],
    resource: ['lead'],
};

export const leadUpdateDescription: INodeProperties[] = [
    {
        displayName: "Lead ID",
        name: "leadId",
        description: 'ID of the lead to update',
        type: 'string',
        required: true,
        default: '',
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
    },
    {
        displayName: "Update Fields",
        name: "updateFields",
        type: "collection",
        placeholder: "Add Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
        options: [
            {
                displayName: "Address",
                name: "address",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'address',
                    },
                },
            },
            {
                displayName: "City",
                name: "city",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'city',
                    },
                },
            },
            {
                displayName: "Company Address",
                name: "companyAddress",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyAddress',
                    },
                },
            },
            {
                displayName: "Company Annual Revenue",
                name: "companyAnnualRevenue",
                type: "number",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyAnnualRevenue',
                    },
                },
            },
            {
                displayName: "Company City",
                name: "companyCity",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyCity',
                    },
                },
            },
            {
                displayName: "Company Name",
                name: "companyName",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyName',
                    },
                },
            },
            {
                displayName: "Company State",
                name: "companyState",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyState',
                    },
                },
            },
            {
                displayName: "Company Website",
                name: "companyWebsite",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyWebsite',
                    },
                },
            },
            {
                displayName: "Company Zipcode",
                name: "companyZipcode",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyZipcode',
                    },
                },
            },
            {
                displayName: "Department",
                name: "department",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'department',
                    },
                },
            },
            {
                displayName: "Designation",
                name: "designation",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'designation',
                    },
                },
            },
            {
                displayName: "Do Not Disturb",
                name: "dnd",
                type: "boolean",
                default: false,
                routing: {
                    send: {
                        type: 'body',
                        property: 'dnd',
                    },
                },
            },
            {
                displayName: "Facebook",
                name: "facebook",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'facebook',
                    },
                },
            },
            {
                displayName: "Linked In",
                name: "linkedIn",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'linkedIn',
                    },
                },
            },
            {
                displayName: "Requirement",
                name: "requirementName",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'requirementName',
                    },
                },
            },
            {
                displayName: "State",
                name: "state",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'state',
                    },
                },
            },
            {
                displayName: "Sub Source",
                name: "subSource",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'subSource',
                    },
                },
            },
            {
                displayName: "Twitter",
                name: "twitter",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'twitter',
                    },
                },
            },
            {
                displayName: "UTM Campaign",
                name: "utmCampaign",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmCampaign',
                    },
                },
            },
            {
                displayName: "UTM Content",
                name: "utmContent",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmContent',
                    },
                },
            },
            {
                displayName: "UTM Medium",
                name: "utmMedium",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmMedium',
                    },
                },
            },
            {
                displayName: "UTM Source",
                name: "utmSource",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmSource',
                    },
                },
            },
            {
                displayName: "UTM Term",
                name: "utmTerm",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmTerm',
                    },
                },
            },
            {
                displayName: "Zipcode",
                name: "zipcode",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'zipcode',
                    },
                },
            },
        ]
    }
];
