"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KylasApi = void 0;
class KylasApi {
    constructor() {
        this.name = 'kylasApi';
        this.displayName = 'Kylas API';
        this.documentationUrl = 'https://github.com/org/-kylas?tab=readme-ov-file#credentials';
        this.properties = [
            {
                displayName: 'API Key',
                name: 'apiKey',
                type: 'string',
                typeOptions: { password: true },
                required: true,
                default: '',
            },
        ];
        this.authenticate = {
            type: 'generic',
            properties: {
                headers: {
                    'api-key': '={{$credentials.apiKey}}',
                },
            },
        };
        this.test = {
            request: {
                baseURL: 'https://api-qa.sling-dev.com',
                url: '/v1/users/me',
            },
        };
    }
}
exports.KylasApi = KylasApi;
//# sourceMappingURL=KylasApi.credentials.js.map