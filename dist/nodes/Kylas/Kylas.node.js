"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Kylas = void 0;
exports.getCachedSystemFields = getCachedSystemFields;
exports.kylasApiRequest = kylasApiRequest;
const user_1 = require("./resources/user");
const company_1 = require("./resources/company");
const lead_1 = require("./resources/lead");
const n8n_workflow_1 = require("n8n-workflow");
class Kylas {
    constructor() {
        this.description = {
            displayName: 'Kyla<PERSON>',
            name: 'kyla<PERSON>',
            icon: { light: 'file:kylas.svg', dark: 'file:kylas.dark.svg' },
            group: ['transform'],
            version: 1,
            subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
            description: 'Interact with the Kylas API',
            defaults: {
                name: '<PERSON>yla<PERSON>',
            },
            usableAsTool: true,
            inputs: ['main'],
            outputs: ['main'],
            credentials: [{ name: 'kylas<PERSON><PERSON>', required: true }],
            requestDefaults: {
                baseURL: 'https://api-qa.sling-dev.com',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
            },
            properties: [
                {
                    displayName: 'Resource',
                    name: 'resource',
                    type: 'options',
                    noDataExpression: true,
                    options: [
                        {
                            name: 'User',
                            value: 'user',
                        },
                        {
                            name: 'Company',
                            value: 'company',
                        },
                        {
                            name: 'Lead',
                            value: 'lead',
                        },
                    ],
                    default: 'user',
                },
                ...user_1.userDescription,
                ...company_1.companyDescription,
                ...lead_1.leadDescription
            ],
        };
        this.methods = {
            loadOptions: {
                async getLeadCustomFields() {
                    console.log("=== getLeadCustomFields CALLED ===");
                    const returnData = [];
                    const fields = await getCachedSystemFields.call(this);
                    fields
                        .filter(field => field.active
                        && field.standard === false
                        && field.type !== 'LOOK_UP'
                        && field.type !== 'MULTI_PICKLIST'
                        && field.type !== 'PICK_LIST')
                        .forEach(field => {
                        returnData.push({
                            name: field.displayName,
                            value: field.name,
                            inputSchema: field.inputSchema,
                        });
                    });
                    console.log(`getLeadCustomFields returning ${returnData.length} fields`);
                    return returnData;
                },
            },
        };
    }
    async execute() {
        return [[]];
    }
}
exports.Kylas = Kylas;
let systemFieldsCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000;
async function getCachedSystemFields() {
    const now = Date.now();
    if (systemFieldsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('Using cached system fields');
        return systemFieldsCache;
    }
    console.log('Fetching fresh system fields from API');
    const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
    const fields = JSON.parse(customFields.data);
    systemFieldsCache = fields;
    cacheTimestamp = now;
    return fields;
}
async function kylasApiRequest(method, endpoint, body) {
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options = {
        headers: {
            Accept: 'application/json',
        },
        method,
        url: `https://api-qa.sling-dev.com${endpoint}`,
    };
    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }
    try {
        const credentialType = 'kylasApi';
        const responseData = await this.helpers.requestWithAuthentication.call(this, credentialType, options);
        if (responseData.success === false) {
            throw new n8n_workflow_1.NodeApiError(this.getNode(), responseData);
        }
        console.log("Return success");
        return {
            data: responseData
        };
    }
    catch (error) {
        throw new n8n_workflow_1.NodeApiError(this.getNode(), error);
    }
}
;
//# sourceMappingURL=Kylas.node.js.map