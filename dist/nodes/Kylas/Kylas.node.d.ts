import { INodeType, INodeTypeDescription, ILoadOptionsFunctions, INodePropertyOptions, IHookFunctions, IExecuteFunctions, IHttpRequestMethods, IDataObject, INodeExecutionData } from 'n8n-workflow';
export declare class Kylas implements INodeType {
    description: INodeTypeDescription;
    methods: {
        loadOptions: {
            getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
        };
    };
    execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]>;
}
export declare function getLeadCustomFieldsAsFields(context: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<Fields>;
export declare function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<RawFieldData[]>;
export declare function kylasApiRequest(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions, method: IHttpRequestMethods, endpoint: string, body: IDataObject): Promise<KylasApiResponse>;
interface KylasApiResponse {
    data: string;
    success?: boolean;
}
interface RawFieldData {
    id: number;
    type: string;
    displayName: string;
    name: string;
    active: boolean;
    required: boolean;
    important: boolean;
    standard: boolean;
    width: number;
    column: number;
    row: number;
    multiValue: boolean;
    internal: boolean;
    picklist: unknown;
    systemRequired: boolean;
    inputSchema?: unknown;
}
export type Fields = {
    fields: Array<{
        name: string;
        displayName: string;
        type: string;
        required: boolean;
        standard: boolean;
        internal: boolean;
    }>;
};
export {};
