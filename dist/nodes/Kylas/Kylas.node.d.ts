import { INodeType, INodeTypeDescription, ILoadOptionsFunctions, INodePropertyOptions, IHookFunctions, IExecuteFunctions, IHttpRequestMethods, IDataObject, INodeExecutionData } from 'n8n-workflow';
export declare class Kylas implements INodeType {
    description: INodeTypeDescription;
    methods: {
        loadOptions: {
            getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
        };
    };
    execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]>;
}
export declare function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<any[]>;
export declare function kylasApiRequest(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions, method: IHttpRequestMethods, endpoint: string, body: IDataObject): Promise<any>;
export type Fields = {
    fields: Array<{
        name: string;
        displayName: string;
        type: string;
        required: boolean;
        standard: boolean;
        internal: boolean;
    }>;
};
