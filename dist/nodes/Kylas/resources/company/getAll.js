"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.companyGetManyDescription = void 0;
const showOnlyForCompanyGetMany = {
    operation: ['getAll'],
    resource: ['company'],
};
exports.companyGetManyDescription = [
    {
        displayName: 'Limit',
        name: 'limit',
        type: 'number',
        displayOptions: {
            show: {
                ...showOnlyForCompanyGetMany,
                returnAll: [false],
            },
        },
        typeOptions: {
            minValue: 1,
            maxValue: 100,
        },
        default: 50,
        routing: {
            send: {
                type: 'query',
                property: 'limit',
            },
            output: {
                maxResults: '={{$value}}',
            },
        },
        description: 'Max number of results to return',
    },
    {
        displayName: 'Return All',
        name: 'returnAll',
        type: 'boolean',
        displayOptions: {
            show: showOnlyForCompanyGetMany,
        },
        default: false,
        description: 'Whether to return all results or only up to a given limit',
        routing: {
            send: {
                paginate: '={{ $value }}',
            },
            operations: {
                pagination: {
                    type: 'offset',
                    properties: {
                        limitParameter: 'limit',
                        offsetParameter: 'offset',
                        pageSize: 100,
                        type: 'query',
                    },
                },
            },
        },
    },
];
//# sourceMappingURL=getAll.js.map