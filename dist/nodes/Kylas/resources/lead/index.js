"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.leadDescription = void 0;
const create_1 = require("./create");
const customFields_1 = require("./customFields");
const update_1 = require("./update");
const customFieldsUpdate_1 = require("./customFieldsUpdate");
const get_1 = require("./get");
const search_1 = require("./search");
const showOnlyForLeads = {
    resource: ['lead'],
};
exports.leadDescription = [
    {
        displayName: 'Operation',
        name: 'operation',
        type: 'options',
        noDataExpression: true,
        displayOptions: {
            show: showOnlyForLeads,
        },
        options: [
            {
                name: 'Get Many',
                value: 'search',
                action: 'Get leads',
                description: 'Get many leads',
                routing: {
                    request: {
                        method: 'GET',
                        url: '/v1/leads',
                    },
                },
            },
            {
                name: 'Get',
                value: 'get',
                action: 'Get a lead',
                description: 'Get the data of a single lead',
                routing: {
                    request: {
                        method: 'GET',
                        url: '=/v1/leads/{{$parameter.leadId}}',
                    },
                },
            },
            {
                name: 'Create',
                value: 'create',
                action: 'Create a new lead',
                description: 'Create a new lead',
                routing: {
                    request: {
                        method: 'POST',
                        url: '/v1/leads',
                    },
                },
            },
            {
                name: 'Update',
                value: 'update',
                action: 'Update a lead',
                description: 'Update an existing lead',
                routing: {
                    request: {
                        method: 'PUT',
                        url: '=/v1/leads/{{$parameter.leadId}}',
                    },
                },
            },
        ],
        default: 'get',
    },
    ...create_1.leadCreateDescription,
    ...customFields_1.customFieldsDescription,
    ...update_1.leadUpdateDescription,
    ...customFieldsUpdate_1.customFieldsUpdateDescription,
    ...get_1.leadGetDescription,
    ...search_1.leadSearchDescription
];
//# sourceMappingURL=index.js.map