"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customFieldsUpdateDescription = void 0;
const showOnlyForLeadUpdate = {
    operation: ['update'],
    resource: ['lead'],
};
exports.customFieldsUpdateDescription = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Property",
                name: "property",
                values: [
                    {
                        displayName: "Field Name or ID",
                        name: "name",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    },
                    {
                        displayName: "Field Value",
                        name: "value",
                        type: "string",
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    }
                ]
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function (requestOptions) {
                        const customFields = this.getNodeParameter('customFields.property', []);
                        if (customFields.length > 0) {
                            if (!requestOptions.body) {
                                requestOptions.body = {};
                            }
                            if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                const body = requestOptions.body;
                                if (!body.customFieldValues) {
                                    body.customFieldValues = {};
                                }
                                const customFieldValues = body.customFieldValues;
                                customFields.forEach(field => {
                                    if (field.name && field.value) {
                                        if (field.value !== undefined && field.value !== '' && field.value !== null) {
                                            customFieldValues[field.name] = field.value;
                                        }
                                    }
                                });
                            }
                        }
                        return requestOptions;
                    }
                ]
            },
        },
    },
];
//# sourceMappingURL=customFieldsUpdate.js.map