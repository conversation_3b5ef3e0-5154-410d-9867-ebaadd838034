{"name": "@inquirer/ansi", "version": "1.0.0", "engines": {"node": ">=18"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh", "ansi"], "sideEffects": false, "files": ["dist"], "devDependencies": {"@arethetypeswrong/cli": "^0.18.2", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "tshy": {"exclude": ["src/**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "scripts": {"tsc": "tshy", "attw": "attw --pack"}, "type": "module", "publishConfig": {"access": "public"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/main/packages/ansi/README.md"}