/** Move cursor to first column */
export declare const cursorLeft: string;
/** Hide the cursor */
export declare const cursorHide: string;
/** Show the cursor */
export declare const cursorShow: string;
/** Move cursor up by count rows */
export declare const cursorUp: (rows?: number) => string;
/** Move cursor down by count rows */
export declare const cursorDown: (rows?: number) => string;
/** Move cursor to position (x, y) */
export declare const cursorTo: (x: number, y?: number) => string;
/** Erase the specified number of lines above the cursor */
export declare const eraseLines: (lines: number) => string;
