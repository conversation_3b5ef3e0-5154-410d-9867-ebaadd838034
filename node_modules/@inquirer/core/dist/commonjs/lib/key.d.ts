export type KeypressEvent = {
    name: string;
    ctrl: boolean;
};
export declare const isUpKey: (key: KeypressEvent) => boolean;
export declare const isDownKey: (key: KeypressEvent) => boolean;
export declare const isSpaceKey: (key: KeypressEvent) => boolean;
export declare const isBackspaceKey: (key: KeypressEvent) => boolean;
export declare const isTabKey: (key: KeypressEvent) => boolean;
export declare const isNumberKey: (key: KeypressEvent) => boolean;
export declare const isEnterKey: (key: KeypressEvent) => boolean;
