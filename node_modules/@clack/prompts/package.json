{"name": "@clack/prompts", "version": "0.11.0", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/bombshell-dev/clack.git", "directory": "packages/prompts"}, "bugs": {"url": "https://github.com/bombshell-dev/clack/issues"}, "homepage": "https://github.com/bombshell-dev/clack/tree/main/packages/prompts#readme", "files": ["dist", "CHANGELOG.md"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/n_moore"}, "license": "MIT", "keywords": ["ask", "clack", "cli", "command-line", "command", "input", "interact", "interface", "menu", "prompt", "prompts", "stdin", "ui"], "dependencies": {"picocolors": "^1.0.0", "sisteransi": "^1.0.5", "@clack/core": "0.5.0"}, "devDependencies": {"is-unicode-supported": "^1.3.0"}, "scripts": {"build": "unbuild"}}