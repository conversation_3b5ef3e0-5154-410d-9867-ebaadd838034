{"version": 3, "file": "index.mjs", "sources": ["../../../node_modules/.pnpm/is-unicode-supported@1.3.0/node_modules/is-unicode-supported/index.js", "../src/index.ts"], "sourcesContent": ["import process from 'node:process';\n\nexport default function isUnicodeSupported() {\n\tif (process.platform !== 'win32') {\n\t\treturn process.env.TERM !== 'linux'; // Linux console (kernel)\n\t}\n\n\treturn Boolean(process.env.CI)\n\t\t|| Boolean(process.env.WT_SESSION) // Windows Terminal\n\t\t|| Boolean(process.env.TERMINUS_SUBLIME) // Terminus (<0.2.27)\n\t\t|| process.env.ConEmuTask === '{cmd::Cmder}' // ConEmu and cmder\n\t\t|| process.env.TERM_PROGRAM === 'Terminus-Sublime'\n\t\t|| process.env.TERM_PROGRAM === 'vscode'\n\t\t|| process.env.TERM === 'xterm-256color'\n\t\t|| process.env.TERM === 'alacritty'\n\t\t|| process.env.TERMINAL_EMULATOR === 'JetBrains-JediTerm';\n}\n", "import { stripVTControlCharacters as strip } from 'node:util';\nimport {\n\tConfirmPrompt,\n\tGroupMultiSelectPrompt,\n\tMultiSelectPrompt,\n\tPasswordPrompt,\n\tSelectKeyPrompt,\n\tSelectPrompt,\n\ttype State,\n\tTextPrompt,\n\tblock,\n\tisCancel,\n} from '@clack/core';\nimport isUnicodeSupported from 'is-unicode-supported';\nimport color from 'picocolors';\nimport { cursor, erase } from 'sisteransi';\n\nexport { isCancel } from '@clack/core';\nexport { updateSettings, type ClackSettings } from '@clack/core';\n\nconst unicode = isUnicodeSupported();\nconst s = (c: string, fallback: string) => (unicode ? c : fallback);\nconst S_STEP_ACTIVE = s('◆', '*');\nconst S_STEP_CANCEL = s('■', 'x');\nconst S_STEP_ERROR = s('▲', 'x');\nconst S_STEP_SUBMIT = s('◇', 'o');\n\nconst S_BAR_START = s('┌', 'T');\nconst S_BAR = s('│', '|');\nconst S_BAR_END = s('└', '—');\n\nconst S_RADIO_ACTIVE = s('●', '>');\nconst S_RADIO_INACTIVE = s('○', ' ');\nconst S_CHECKBOX_ACTIVE = s('◻', '[•]');\nconst S_CHECKBOX_SELECTED = s('◼', '[+]');\nconst S_CHECKBOX_INACTIVE = s('◻', '[ ]');\nconst S_PASSWORD_MASK = s('▪', '•');\n\nconst S_BAR_H = s('─', '-');\nconst S_CORNER_TOP_RIGHT = s('╮', '+');\nconst S_CONNECT_LEFT = s('├', '+');\nconst S_CORNER_BOTTOM_RIGHT = s('╯', '+');\n\nconst S_INFO = s('●', '•');\nconst S_SUCCESS = s('◆', '*');\nconst S_WARN = s('▲', '!');\nconst S_ERROR = s('■', 'x');\n\nconst symbol = (state: State) => {\n\tswitch (state) {\n\t\tcase 'initial':\n\t\tcase 'active':\n\t\t\treturn color.cyan(S_STEP_ACTIVE);\n\t\tcase 'cancel':\n\t\t\treturn color.red(S_STEP_CANCEL);\n\t\tcase 'error':\n\t\t\treturn color.yellow(S_STEP_ERROR);\n\t\tcase 'submit':\n\t\t\treturn color.green(S_STEP_SUBMIT);\n\t}\n};\n\ninterface LimitOptionsParams<TOption> {\n\toptions: TOption[];\n\tmaxItems: number | undefined;\n\tcursor: number;\n\tstyle: (option: TOption, active: boolean) => string;\n}\n\nconst limitOptions = <TOption>(params: LimitOptionsParams<TOption>): string[] => {\n\tconst { cursor, options, style } = params;\n\n\tconst paramMaxItems = params.maxItems ?? Number.POSITIVE_INFINITY;\n\tconst outputMaxItems = Math.max(process.stdout.rows - 4, 0);\n\t// We clamp to minimum 5 because anything less doesn't make sense UX wise\n\tconst maxItems = Math.min(outputMaxItems, Math.max(paramMaxItems, 5));\n\tlet slidingWindowLocation = 0;\n\n\tif (cursor >= slidingWindowLocation + maxItems - 3) {\n\t\tslidingWindowLocation = Math.max(Math.min(cursor - maxItems + 3, options.length - maxItems), 0);\n\t} else if (cursor < slidingWindowLocation + 2) {\n\t\tslidingWindowLocation = Math.max(cursor - 2, 0);\n\t}\n\n\tconst shouldRenderTopEllipsis = maxItems < options.length && slidingWindowLocation > 0;\n\tconst shouldRenderBottomEllipsis =\n\t\tmaxItems < options.length && slidingWindowLocation + maxItems < options.length;\n\n\treturn options\n\t\t.slice(slidingWindowLocation, slidingWindowLocation + maxItems)\n\t\t.map((option, i, arr) => {\n\t\t\tconst isTopLimit = i === 0 && shouldRenderTopEllipsis;\n\t\t\tconst isBottomLimit = i === arr.length - 1 && shouldRenderBottomEllipsis;\n\t\t\treturn isTopLimit || isBottomLimit\n\t\t\t\t? color.dim('...')\n\t\t\t\t: style(option, i + slidingWindowLocation === cursor);\n\t\t});\n};\n\nexport interface TextOptions {\n\tmessage: string;\n\tplaceholder?: string;\n\tdefaultValue?: string;\n\tinitialValue?: string;\n\tvalidate?: (value: string) => string | Error | undefined;\n}\nexport const text = (opts: TextOptions) => {\n\treturn new TextPrompt({\n\t\tvalidate: opts.validate,\n\t\tplaceholder: opts.placeholder,\n\t\tdefaultValue: opts.defaultValue,\n\t\tinitialValue: opts.initialValue,\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\t\t\tconst placeholder = opts.placeholder\n\t\t\t\t? color.inverse(opts.placeholder[0]) + color.dim(opts.placeholder.slice(1))\n\t\t\t\t: color.inverse(color.hidden('_'));\n\t\t\tconst value = !this.value ? placeholder : this.valueWithCursor;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'error':\n\t\t\t\t\treturn `${title.trim()}\\n${color.yellow(S_BAR)}  ${value}\\n${color.yellow(\n\t\t\t\t\t\tS_BAR_END\n\t\t\t\t\t)}  ${color.yellow(this.error)}\\n`;\n\t\t\t\tcase 'submit':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.dim(this.value || opts.placeholder)}`;\n\t\t\t\tcase 'cancel':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.strikethrough(\n\t\t\t\t\t\tcolor.dim(this.value ?? '')\n\t\t\t\t\t)}${this.value?.trim() ? `\\n${color.gray(S_BAR)}` : ''}`;\n\t\t\t\tdefault:\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${value}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<string | symbol>;\n};\n\nexport interface PasswordOptions {\n\tmessage: string;\n\tmask?: string;\n\tvalidate?: (value: string) => string | Error | undefined;\n}\nexport const password = (opts: PasswordOptions) => {\n\treturn new PasswordPrompt({\n\t\tvalidate: opts.validate,\n\t\tmask: opts.mask ?? S_PASSWORD_MASK,\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\t\t\tconst value = this.valueWithCursor;\n\t\t\tconst masked = this.masked;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'error':\n\t\t\t\t\treturn `${title.trim()}\\n${color.yellow(S_BAR)}  ${masked}\\n${color.yellow(\n\t\t\t\t\t\tS_BAR_END\n\t\t\t\t\t)}  ${color.yellow(this.error)}\\n`;\n\t\t\t\tcase 'submit':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.dim(masked)}`;\n\t\t\t\tcase 'cancel':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.strikethrough(color.dim(masked ?? ''))}${\n\t\t\t\t\t\tmasked ? `\\n${color.gray(S_BAR)}` : ''\n\t\t\t\t\t}`;\n\t\t\t\tdefault:\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${value}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<string | symbol>;\n};\n\nexport interface ConfirmOptions {\n\tmessage: string;\n\tactive?: string;\n\tinactive?: string;\n\tinitialValue?: boolean;\n}\nexport const confirm = (opts: ConfirmOptions) => {\n\tconst active = opts.active ?? 'Yes';\n\tconst inactive = opts.inactive ?? 'No';\n\treturn new ConfirmPrompt({\n\t\tactive,\n\t\tinactive,\n\t\tinitialValue: opts.initialValue ?? true,\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\t\t\tconst value = this.value ? active : inactive;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'submit':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.dim(value)}`;\n\t\t\t\tcase 'cancel':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${color.strikethrough(\n\t\t\t\t\t\tcolor.dim(value)\n\t\t\t\t\t)}\\n${color.gray(S_BAR)}`;\n\t\t\t\tdefault: {\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${\n\t\t\t\t\t\tthis.value\n\t\t\t\t\t\t\t? `${color.green(S_RADIO_ACTIVE)} ${active}`\n\t\t\t\t\t\t\t: `${color.dim(S_RADIO_INACTIVE)} ${color.dim(active)}`\n\t\t\t\t\t} ${color.dim('/')} ${\n\t\t\t\t\t\t!this.value\n\t\t\t\t\t\t\t? `${color.green(S_RADIO_ACTIVE)} ${inactive}`\n\t\t\t\t\t\t\t: `${color.dim(S_RADIO_INACTIVE)} ${color.dim(inactive)}`\n\t\t\t\t\t}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<boolean | symbol>;\n};\n\ntype Primitive = Readonly<string | boolean | number>;\n\nexport type Option<Value> = Value extends Primitive\n\t? {\n\t\t\t/**\n\t\t\t * Internal data for this option.\n\t\t\t */\n\t\t\tvalue: Value;\n\t\t\t/**\n\t\t\t * The optional, user-facing text for this option.\n\t\t\t *\n\t\t\t * By default, the `value` is converted to a string.\n\t\t\t */\n\t\t\tlabel?: string;\n\t\t\t/**\n\t\t\t * An optional hint to display to the user when\n\t\t\t * this option might be selected.\n\t\t\t *\n\t\t\t * By default, no `hint` is displayed.\n\t\t\t */\n\t\t\thint?: string;\n\t\t}\n\t: {\n\t\t\t/**\n\t\t\t * Internal data for this option.\n\t\t\t */\n\t\t\tvalue: Value;\n\t\t\t/**\n\t\t\t * Required. The user-facing text for this option.\n\t\t\t */\n\t\t\tlabel: string;\n\t\t\t/**\n\t\t\t * An optional hint to display to the user when\n\t\t\t * this option might be selected.\n\t\t\t *\n\t\t\t * By default, no `hint` is displayed.\n\t\t\t */\n\t\t\thint?: string;\n\t\t};\n\nexport interface SelectOptions<Value> {\n\tmessage: string;\n\toptions: Option<Value>[];\n\tinitialValue?: Value;\n\tmaxItems?: number;\n}\n\nexport const select = <Value>(opts: SelectOptions<Value>) => {\n\tconst opt = (option: Option<Value>, state: 'inactive' | 'active' | 'selected' | 'cancelled') => {\n\t\tconst label = option.label ?? String(option.value);\n\t\tswitch (state) {\n\t\t\tcase 'selected':\n\t\t\t\treturn `${color.dim(label)}`;\n\t\t\tcase 'active':\n\t\t\t\treturn `${color.green(S_RADIO_ACTIVE)} ${label} ${\n\t\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t\t}`;\n\t\t\tcase 'cancelled':\n\t\t\t\treturn `${color.strikethrough(color.dim(label))}`;\n\t\t\tdefault:\n\t\t\t\treturn `${color.dim(S_RADIO_INACTIVE)} ${color.dim(label)}`;\n\t\t}\n\t};\n\n\treturn new SelectPrompt({\n\t\toptions: opts.options,\n\t\tinitialValue: opts.initialValue,\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'submit':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${opt(this.options[this.cursor], 'selected')}`;\n\t\t\t\tcase 'cancel':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${opt(\n\t\t\t\t\t\tthis.options[this.cursor],\n\t\t\t\t\t\t'cancelled'\n\t\t\t\t\t)}\\n${color.gray(S_BAR)}`;\n\t\t\t\tdefault: {\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${limitOptions({\n\t\t\t\t\t\tcursor: this.cursor,\n\t\t\t\t\t\toptions: this.options,\n\t\t\t\t\t\tmaxItems: opts.maxItems,\n\t\t\t\t\t\tstyle: (item, active) => opt(item, active ? 'active' : 'inactive'),\n\t\t\t\t\t}).join(`\\n${color.cyan(S_BAR)}  `)}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<Value | symbol>;\n};\n\nexport const selectKey = <Value extends string>(opts: SelectOptions<Value>) => {\n\tconst opt = (\n\t\toption: Option<Value>,\n\t\tstate: 'inactive' | 'active' | 'selected' | 'cancelled' = 'inactive'\n\t) => {\n\t\tconst label = option.label ?? String(option.value);\n\t\tif (state === 'selected') {\n\t\t\treturn `${color.dim(label)}`;\n\t\t}\n\t\tif (state === 'cancelled') {\n\t\t\treturn `${color.strikethrough(color.dim(label))}`;\n\t\t}\n\t\tif (state === 'active') {\n\t\t\treturn `${color.bgCyan(color.gray(` ${option.value} `))} ${label} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\treturn `${color.gray(color.bgWhite(color.inverse(` ${option.value} `)))} ${label} ${\n\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t}`;\n\t};\n\n\treturn new SelectKeyPrompt({\n\t\toptions: opts.options,\n\t\tinitialValue: opts.initialValue,\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'submit':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${opt(\n\t\t\t\t\t\tthis.options.find((opt) => opt.value === this.value) ?? opts.options[0],\n\t\t\t\t\t\t'selected'\n\t\t\t\t\t)}`;\n\t\t\t\tcase 'cancel':\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${opt(this.options[0], 'cancelled')}\\n${color.gray(\n\t\t\t\t\t\tS_BAR\n\t\t\t\t\t)}`;\n\t\t\t\tdefault: {\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${this.options\n\t\t\t\t\t\t.map((option, i) => opt(option, i === this.cursor ? 'active' : 'inactive'))\n\t\t\t\t\t\t.join(`\\n${color.cyan(S_BAR)}  `)}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<Value | symbol>;\n};\n\nexport interface MultiSelectOptions<Value> {\n\tmessage: string;\n\toptions: Option<Value>[];\n\tinitialValues?: Value[];\n\tmaxItems?: number;\n\trequired?: boolean;\n\tcursorAt?: Value;\n}\nexport const multiselect = <Value>(opts: MultiSelectOptions<Value>) => {\n\tconst opt = (\n\t\toption: Option<Value>,\n\t\tstate: 'inactive' | 'active' | 'selected' | 'active-selected' | 'submitted' | 'cancelled'\n\t) => {\n\t\tconst label = option.label ?? String(option.value);\n\t\tif (state === 'active') {\n\t\t\treturn `${color.cyan(S_CHECKBOX_ACTIVE)} ${label} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'selected') {\n\t\t\treturn `${color.green(S_CHECKBOX_SELECTED)} ${color.dim(label)} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'cancelled') {\n\t\t\treturn `${color.strikethrough(color.dim(label))}`;\n\t\t}\n\t\tif (state === 'active-selected') {\n\t\t\treturn `${color.green(S_CHECKBOX_SELECTED)} ${label} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'submitted') {\n\t\t\treturn `${color.dim(label)}`;\n\t\t}\n\t\treturn `${color.dim(S_CHECKBOX_INACTIVE)} ${color.dim(label)}`;\n\t};\n\n\treturn new MultiSelectPrompt({\n\t\toptions: opts.options,\n\t\tinitialValues: opts.initialValues,\n\t\trequired: opts.required ?? true,\n\t\tcursorAt: opts.cursorAt,\n\t\tvalidate(selected: Value[]) {\n\t\t\tif (this.required && selected.length === 0)\n\t\t\t\treturn `Please select at least one option.\\n${color.reset(\n\t\t\t\t\tcolor.dim(\n\t\t\t\t\t\t`Press ${color.gray(color.bgWhite(color.inverse(' space ')))} to select, ${color.gray(\n\t\t\t\t\t\t\tcolor.bgWhite(color.inverse(' enter '))\n\t\t\t\t\t\t)} to submit`\n\t\t\t\t\t)\n\t\t\t\t)}`;\n\t\t},\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\n\t\t\tconst styleOption = (option: Option<Value>, active: boolean) => {\n\t\t\t\tconst selected = this.value.includes(option.value);\n\t\t\t\tif (active && selected) {\n\t\t\t\t\treturn opt(option, 'active-selected');\n\t\t\t\t}\n\t\t\t\tif (selected) {\n\t\t\t\t\treturn opt(option, 'selected');\n\t\t\t\t}\n\t\t\t\treturn opt(option, active ? 'active' : 'inactive');\n\t\t\t};\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'submit': {\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${\n\t\t\t\t\t\tthis.options\n\t\t\t\t\t\t\t.filter(({ value }) => this.value.includes(value))\n\t\t\t\t\t\t\t.map((option) => opt(option, 'submitted'))\n\t\t\t\t\t\t\t.join(color.dim(', ')) || color.dim('none')\n\t\t\t\t\t}`;\n\t\t\t\t}\n\t\t\t\tcase 'cancel': {\n\t\t\t\t\tconst label = this.options\n\t\t\t\t\t\t.filter(({ value }) => this.value.includes(value))\n\t\t\t\t\t\t.map((option) => opt(option, 'cancelled'))\n\t\t\t\t\t\t.join(color.dim(', '));\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${\n\t\t\t\t\t\tlabel.trim() ? `${label}\\n${color.gray(S_BAR)}` : ''\n\t\t\t\t\t}`;\n\t\t\t\t}\n\t\t\t\tcase 'error': {\n\t\t\t\t\tconst footer = this.error\n\t\t\t\t\t\t.split('\\n')\n\t\t\t\t\t\t.map((ln, i) =>\n\t\t\t\t\t\t\ti === 0 ? `${color.yellow(S_BAR_END)}  ${color.yellow(ln)}` : `   ${ln}`\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.join('\\n');\n\t\t\t\t\treturn `${title + color.yellow(S_BAR)}  ${limitOptions({\n\t\t\t\t\t\toptions: this.options,\n\t\t\t\t\t\tcursor: this.cursor,\n\t\t\t\t\t\tmaxItems: opts.maxItems,\n\t\t\t\t\t\tstyle: styleOption,\n\t\t\t\t\t}).join(`\\n${color.yellow(S_BAR)}  `)}\\n${footer}\\n`;\n\t\t\t\t}\n\t\t\t\tdefault: {\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${limitOptions({\n\t\t\t\t\t\toptions: this.options,\n\t\t\t\t\t\tcursor: this.cursor,\n\t\t\t\t\t\tmaxItems: opts.maxItems,\n\t\t\t\t\t\tstyle: styleOption,\n\t\t\t\t\t}).join(`\\n${color.cyan(S_BAR)}  `)}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<Value[] | symbol>;\n};\n\nexport interface GroupMultiSelectOptions<Value> {\n\tmessage: string;\n\toptions: Record<string, Option<Value>[]>;\n\tinitialValues?: Value[];\n\trequired?: boolean;\n\tcursorAt?: Value;\n\tselectableGroups?: boolean;\n}\nexport const groupMultiselect = <Value>(opts: GroupMultiSelectOptions<Value>) => {\n\tconst { selectableGroups = true } = opts;\n\tconst opt = (\n\t\toption: Option<Value>,\n\t\tstate:\n\t\t\t| 'inactive'\n\t\t\t| 'active'\n\t\t\t| 'selected'\n\t\t\t| 'active-selected'\n\t\t\t| 'group-active'\n\t\t\t| 'group-active-selected'\n\t\t\t| 'submitted'\n\t\t\t| 'cancelled',\n\t\toptions: Option<Value>[] = []\n\t) => {\n\t\tconst label = option.label ?? String(option.value);\n\t\tconst isItem = typeof (option as any).group === 'string';\n\t\tconst next = isItem && (options[options.indexOf(option) + 1] ?? { group: true });\n\t\tconst isLast = isItem && (next as any).group === true;\n\t\tconst prefix = isItem ? (selectableGroups ? `${isLast ? S_BAR_END : S_BAR} ` : '  ') : '';\n\n\t\tif (state === 'active') {\n\t\t\treturn `${color.dim(prefix)}${color.cyan(S_CHECKBOX_ACTIVE)} ${label} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'group-active') {\n\t\t\treturn `${prefix}${color.cyan(S_CHECKBOX_ACTIVE)} ${color.dim(label)}`;\n\t\t}\n\t\tif (state === 'group-active-selected') {\n\t\t\treturn `${prefix}${color.green(S_CHECKBOX_SELECTED)} ${color.dim(label)}`;\n\t\t}\n\t\tif (state === 'selected') {\n\t\t\tconst selectedCheckbox = isItem || selectableGroups ? color.green(S_CHECKBOX_SELECTED) : '';\n\t\t\treturn `${color.dim(prefix)}${selectedCheckbox} ${color.dim(label)} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'cancelled') {\n\t\t\treturn `${color.strikethrough(color.dim(label))}`;\n\t\t}\n\t\tif (state === 'active-selected') {\n\t\t\treturn `${color.dim(prefix)}${color.green(S_CHECKBOX_SELECTED)} ${label} ${\n\t\t\t\toption.hint ? color.dim(`(${option.hint})`) : ''\n\t\t\t}`;\n\t\t}\n\t\tif (state === 'submitted') {\n\t\t\treturn `${color.dim(label)}`;\n\t\t}\n\t\tconst unselectedCheckbox = isItem || selectableGroups ? color.dim(S_CHECKBOX_INACTIVE) : '';\n\t\treturn `${color.dim(prefix)}${unselectedCheckbox} ${color.dim(label)}`;\n\t};\n\n\treturn new GroupMultiSelectPrompt({\n\t\toptions: opts.options,\n\t\tinitialValues: opts.initialValues,\n\t\trequired: opts.required ?? true,\n\t\tcursorAt: opts.cursorAt,\n\t\tselectableGroups,\n\t\tvalidate(selected: Value[]) {\n\t\t\tif (this.required && selected.length === 0)\n\t\t\t\treturn `Please select at least one option.\\n${color.reset(\n\t\t\t\t\tcolor.dim(\n\t\t\t\t\t\t`Press ${color.gray(color.bgWhite(color.inverse(' space ')))} to select, ${color.gray(\n\t\t\t\t\t\t\tcolor.bgWhite(color.inverse(' enter '))\n\t\t\t\t\t\t)} to submit`\n\t\t\t\t\t)\n\t\t\t\t)}`;\n\t\t},\n\t\trender() {\n\t\t\tconst title = `${color.gray(S_BAR)}\\n${symbol(this.state)}  ${opts.message}\\n`;\n\n\t\t\tswitch (this.state) {\n\t\t\t\tcase 'submit': {\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${this.options\n\t\t\t\t\t\t.filter(({ value }) => this.value.includes(value))\n\t\t\t\t\t\t.map((option) => opt(option, 'submitted'))\n\t\t\t\t\t\t.join(color.dim(', '))}`;\n\t\t\t\t}\n\t\t\t\tcase 'cancel': {\n\t\t\t\t\tconst label = this.options\n\t\t\t\t\t\t.filter(({ value }) => this.value.includes(value))\n\t\t\t\t\t\t.map((option) => opt(option, 'cancelled'))\n\t\t\t\t\t\t.join(color.dim(', '));\n\t\t\t\t\treturn `${title}${color.gray(S_BAR)}  ${\n\t\t\t\t\t\tlabel.trim() ? `${label}\\n${color.gray(S_BAR)}` : ''\n\t\t\t\t\t}`;\n\t\t\t\t}\n\t\t\t\tcase 'error': {\n\t\t\t\t\tconst footer = this.error\n\t\t\t\t\t\t.split('\\n')\n\t\t\t\t\t\t.map((ln, i) =>\n\t\t\t\t\t\t\ti === 0 ? `${color.yellow(S_BAR_END)}  ${color.yellow(ln)}` : `   ${ln}`\n\t\t\t\t\t\t)\n\t\t\t\t\t\t.join('\\n');\n\t\t\t\t\treturn `${title}${color.yellow(S_BAR)}  ${this.options\n\t\t\t\t\t\t.map((option, i, options) => {\n\t\t\t\t\t\t\tconst selected =\n\t\t\t\t\t\t\t\tthis.value.includes(option.value) ||\n\t\t\t\t\t\t\t\t(option.group === true && this.isGroupSelected(`${option.value}`));\n\t\t\t\t\t\t\tconst active = i === this.cursor;\n\t\t\t\t\t\t\tconst groupActive =\n\t\t\t\t\t\t\t\t!active &&\n\t\t\t\t\t\t\t\ttypeof option.group === 'string' &&\n\t\t\t\t\t\t\t\tthis.options[this.cursor].value === option.group;\n\t\t\t\t\t\t\tif (groupActive) {\n\t\t\t\t\t\t\t\treturn opt(option, selected ? 'group-active-selected' : 'group-active', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (active && selected) {\n\t\t\t\t\t\t\t\treturn opt(option, 'active-selected', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (selected) {\n\t\t\t\t\t\t\t\treturn opt(option, 'selected', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn opt(option, active ? 'active' : 'inactive', options);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.join(`\\n${color.yellow(S_BAR)}  `)}\\n${footer}\\n`;\n\t\t\t\t}\n\t\t\t\tdefault: {\n\t\t\t\t\treturn `${title}${color.cyan(S_BAR)}  ${this.options\n\t\t\t\t\t\t.map((option, i, options) => {\n\t\t\t\t\t\t\tconst selected =\n\t\t\t\t\t\t\t\tthis.value.includes(option.value) ||\n\t\t\t\t\t\t\t\t(option.group === true && this.isGroupSelected(`${option.value}`));\n\t\t\t\t\t\t\tconst active = i === this.cursor;\n\t\t\t\t\t\t\tconst groupActive =\n\t\t\t\t\t\t\t\t!active &&\n\t\t\t\t\t\t\t\ttypeof option.group === 'string' &&\n\t\t\t\t\t\t\t\tthis.options[this.cursor].value === option.group;\n\t\t\t\t\t\t\tif (groupActive) {\n\t\t\t\t\t\t\t\treturn opt(option, selected ? 'group-active-selected' : 'group-active', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (active && selected) {\n\t\t\t\t\t\t\t\treturn opt(option, 'active-selected', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (selected) {\n\t\t\t\t\t\t\t\treturn opt(option, 'selected', options);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn opt(option, active ? 'active' : 'inactive', options);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.join(`\\n${color.cyan(S_BAR)}  `)}\\n${color.cyan(S_BAR_END)}\\n`;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t}).prompt() as Promise<Value[] | symbol>;\n};\n\nexport const note = (message = '', title = '') => {\n\tconst lines = `\\n${message}\\n`.split('\\n');\n\tconst titleLen = strip(title).length;\n\tconst len =\n\t\tMath.max(\n\t\t\tlines.reduce((sum, ln) => {\n\t\t\t\tconst line = strip(ln);\n\t\t\t\treturn line.length > sum ? line.length : sum;\n\t\t\t}, 0),\n\t\t\ttitleLen\n\t\t) + 2;\n\tconst msg = lines\n\t\t.map(\n\t\t\t(ln) =>\n\t\t\t\t`${color.gray(S_BAR)}  ${color.dim(ln)}${' '.repeat(len - strip(ln).length)}${color.gray(\n\t\t\t\t\tS_BAR\n\t\t\t\t)}`\n\t\t)\n\t\t.join('\\n');\n\tprocess.stdout.write(\n\t\t`${color.gray(S_BAR)}\\n${color.green(S_STEP_SUBMIT)}  ${color.reset(title)} ${color.gray(\n\t\t\tS_BAR_H.repeat(Math.max(len - titleLen - 1, 1)) + S_CORNER_TOP_RIGHT\n\t\t)}\\n${msg}\\n${color.gray(S_CONNECT_LEFT + S_BAR_H.repeat(len + 2) + S_CORNER_BOTTOM_RIGHT)}\\n`\n\t);\n};\n\nexport const cancel = (message = '') => {\n\tprocess.stdout.write(`${color.gray(S_BAR_END)}  ${color.red(message)}\\n\\n`);\n};\n\nexport const intro = (title = '') => {\n\tprocess.stdout.write(`${color.gray(S_BAR_START)}  ${title}\\n`);\n};\n\nexport const outro = (message = '') => {\n\tprocess.stdout.write(`${color.gray(S_BAR)}\\n${color.gray(S_BAR_END)}  ${message}\\n\\n`);\n};\n\nexport type LogMessageOptions = {\n\tsymbol?: string;\n};\nexport const log = {\n\tmessage: (message = '', { symbol = color.gray(S_BAR) }: LogMessageOptions = {}) => {\n\t\tconst parts = [`${color.gray(S_BAR)}`];\n\t\tif (message) {\n\t\t\tconst [firstLine, ...lines] = message.split('\\n');\n\t\t\tparts.push(`${symbol}  ${firstLine}`, ...lines.map((ln) => `${color.gray(S_BAR)}  ${ln}`));\n\t\t}\n\t\tprocess.stdout.write(`${parts.join('\\n')}\\n`);\n\t},\n\tinfo: (message: string) => {\n\t\tlog.message(message, { symbol: color.blue(S_INFO) });\n\t},\n\tsuccess: (message: string) => {\n\t\tlog.message(message, { symbol: color.green(S_SUCCESS) });\n\t},\n\tstep: (message: string) => {\n\t\tlog.message(message, { symbol: color.green(S_STEP_SUBMIT) });\n\t},\n\twarn: (message: string) => {\n\t\tlog.message(message, { symbol: color.yellow(S_WARN) });\n\t},\n\t/** alias for `log.warn()`. */\n\twarning: (message: string) => {\n\t\tlog.warn(message);\n\t},\n\terror: (message: string) => {\n\t\tlog.message(message, { symbol: color.red(S_ERROR) });\n\t},\n};\n\nconst prefix = `${color.gray(S_BAR)}  `;\nexport const stream = {\n\tmessage: async (\n\t\titerable: Iterable<string> | AsyncIterable<string>,\n\t\t{ symbol = color.gray(S_BAR) }: LogMessageOptions = {}\n\t) => {\n\t\tprocess.stdout.write(`${color.gray(S_BAR)}\\n${symbol}  `);\n\t\tlet lineWidth = 3;\n\t\tfor await (let chunk of iterable) {\n\t\t\tchunk = chunk.replace(/\\n/g, `\\n${prefix}`);\n\t\t\tif (chunk.includes('\\n')) {\n\t\t\t\tlineWidth = 3 + strip(chunk.slice(chunk.lastIndexOf('\\n'))).length;\n\t\t\t}\n\t\t\tconst chunkLen = strip(chunk).length;\n\t\t\tif (lineWidth + chunkLen < process.stdout.columns) {\n\t\t\t\tlineWidth += chunkLen;\n\t\t\t\tprocess.stdout.write(chunk);\n\t\t\t} else {\n\t\t\t\tprocess.stdout.write(`\\n${prefix}${chunk.trimStart()}`);\n\t\t\t\tlineWidth = 3 + strip(chunk.trimStart()).length;\n\t\t\t}\n\t\t}\n\t\tprocess.stdout.write('\\n');\n\t},\n\tinfo: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.message(iterable, { symbol: color.blue(S_INFO) });\n\t},\n\tsuccess: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.message(iterable, { symbol: color.green(S_SUCCESS) });\n\t},\n\tstep: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.message(iterable, { symbol: color.green(S_STEP_SUBMIT) });\n\t},\n\twarn: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.message(iterable, { symbol: color.yellow(S_WARN) });\n\t},\n\t/** alias for `log.warn()`. */\n\twarning: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.warn(iterable);\n\t},\n\terror: (iterable: Iterable<string> | AsyncIterable<string>) => {\n\t\treturn stream.message(iterable, { symbol: color.red(S_ERROR) });\n\t},\n};\n\nexport interface SpinnerOptions {\n\tindicator?: 'dots' | 'timer';\n}\n\nexport const spinner = ({ indicator = 'dots' }: SpinnerOptions = {}) => {\n\tconst frames = unicode ? ['◒', '◐', '◓', '◑'] : ['•', 'o', 'O', '0'];\n\tconst delay = unicode ? 80 : 120;\n\tconst isCI = process.env.CI === 'true';\n\n\tlet unblock: () => void;\n\tlet loop: NodeJS.Timeout;\n\tlet isSpinnerActive = false;\n\tlet _message = '';\n\tlet _prevMessage: string | undefined = undefined;\n\tlet _origin: number = performance.now();\n\n\tconst handleExit = (code: number) => {\n\t\tconst msg = code > 1 ? 'Something went wrong' : 'Canceled';\n\t\tif (isSpinnerActive) stop(msg, code);\n\t};\n\n\tconst errorEventHandler = () => handleExit(2);\n\tconst signalEventHandler = () => handleExit(1);\n\n\tconst registerHooks = () => {\n\t\t// Reference: https://nodejs.org/api/process.html#event-uncaughtexception\n\t\tprocess.on('uncaughtExceptionMonitor', errorEventHandler);\n\t\t// Reference: https://nodejs.org/api/process.html#event-unhandledrejection\n\t\tprocess.on('unhandledRejection', errorEventHandler);\n\t\t// Reference Signal Events: https://nodejs.org/api/process.html#signal-events\n\t\tprocess.on('SIGINT', signalEventHandler);\n\t\tprocess.on('SIGTERM', signalEventHandler);\n\t\tprocess.on('exit', handleExit);\n\t};\n\n\tconst clearHooks = () => {\n\t\tprocess.removeListener('uncaughtExceptionMonitor', errorEventHandler);\n\t\tprocess.removeListener('unhandledRejection', errorEventHandler);\n\t\tprocess.removeListener('SIGINT', signalEventHandler);\n\t\tprocess.removeListener('SIGTERM', signalEventHandler);\n\t\tprocess.removeListener('exit', handleExit);\n\t};\n\n\tconst clearPrevMessage = () => {\n\t\tif (_prevMessage === undefined) return;\n\t\tif (isCI) process.stdout.write('\\n');\n\t\tconst prevLines = _prevMessage.split('\\n');\n\t\tprocess.stdout.write(cursor.move(-999, prevLines.length - 1));\n\t\tprocess.stdout.write(erase.down(prevLines.length));\n\t};\n\n\tconst parseMessage = (msg: string): string => {\n\t\treturn msg.replace(/\\.+$/, '');\n\t};\n\n\tconst formatTimer = (origin: number): string => {\n\t\tconst duration = (performance.now() - origin) / 1000;\n\t\tconst min = Math.floor(duration / 60);\n\t\tconst secs = Math.floor(duration % 60);\n\t\treturn min > 0 ? `[${min}m ${secs}s]` : `[${secs}s]`;\n\t};\n\n\tconst start = (msg = ''): void => {\n\t\tisSpinnerActive = true;\n\t\tunblock = block();\n\t\t_message = parseMessage(msg);\n\t\t_origin = performance.now();\n\t\tprocess.stdout.write(`${color.gray(S_BAR)}\\n`);\n\t\tlet frameIndex = 0;\n\t\tlet indicatorTimer = 0;\n\t\tregisterHooks();\n\t\tloop = setInterval(() => {\n\t\t\tif (isCI && _message === _prevMessage) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tclearPrevMessage();\n\t\t\t_prevMessage = _message;\n\t\t\tconst frame = color.magenta(frames[frameIndex]);\n\n\t\t\tif (isCI) {\n\t\t\t\tprocess.stdout.write(`${frame}  ${_message}...`);\n\t\t\t} else if (indicator === 'timer') {\n\t\t\t\tprocess.stdout.write(`${frame}  ${_message} ${formatTimer(_origin)}`);\n\t\t\t} else {\n\t\t\t\tconst loadingDots = '.'.repeat(Math.floor(indicatorTimer)).slice(0, 3);\n\t\t\t\tprocess.stdout.write(`${frame}  ${_message}${loadingDots}`);\n\t\t\t}\n\n\t\t\tframeIndex = frameIndex + 1 < frames.length ? frameIndex + 1 : 0;\n\t\t\tindicatorTimer = indicatorTimer < frames.length ? indicatorTimer + 0.125 : 0;\n\t\t}, delay);\n\t};\n\n\tconst stop = (msg = '', code = 0): void => {\n\t\tisSpinnerActive = false;\n\t\tclearInterval(loop);\n\t\tclearPrevMessage();\n\t\tconst step =\n\t\t\tcode === 0\n\t\t\t\t? color.green(S_STEP_SUBMIT)\n\t\t\t\t: code === 1\n\t\t\t\t\t? color.red(S_STEP_CANCEL)\n\t\t\t\t\t: color.red(S_STEP_ERROR);\n\t\t_message = parseMessage(msg ?? _message);\n\t\tif (indicator === 'timer') {\n\t\t\tprocess.stdout.write(`${step}  ${_message} ${formatTimer(_origin)}\\n`);\n\t\t} else {\n\t\t\tprocess.stdout.write(`${step}  ${_message}\\n`);\n\t\t}\n\t\tclearHooks();\n\t\tunblock();\n\t};\n\n\tconst message = (msg = ''): void => {\n\t\t_message = parseMessage(msg ?? _message);\n\t};\n\n\treturn {\n\t\tstart,\n\t\tstop,\n\t\tmessage,\n\t};\n};\n\nexport type PromptGroupAwaitedReturn<T> = {\n\t[P in keyof T]: Exclude<Awaited<T[P]>, symbol>;\n};\n\nexport interface PromptGroupOptions<T> {\n\t/**\n\t * Control how the group can be canceled\n\t * if one of the prompts is canceled.\n\t */\n\tonCancel?: (opts: { results: Prettify<Partial<PromptGroupAwaitedReturn<T>>> }) => void;\n}\n\ntype Prettify<T> = {\n\t[P in keyof T]: T[P];\n} & {};\n\nexport type PromptGroup<T> = {\n\t[P in keyof T]: (opts: {\n\t\tresults: Prettify<Partial<PromptGroupAwaitedReturn<Omit<T, P>>>>;\n\t}) => undefined | Promise<T[P] | undefined>;\n};\n\n/**\n * Define a group of prompts to be displayed\n * and return a results of objects within the group\n */\nexport const group = async <T>(\n\tprompts: PromptGroup<T>,\n\topts?: PromptGroupOptions<T>\n): Promise<Prettify<PromptGroupAwaitedReturn<T>>> => {\n\tconst results = {} as any;\n\tconst promptNames = Object.keys(prompts);\n\n\tfor (const name of promptNames) {\n\t\tconst prompt = prompts[name as keyof T];\n\t\tconst result = await prompt({ results })?.catch((e) => {\n\t\t\tthrow e;\n\t\t});\n\n\t\t// Pass the results to the onCancel function\n\t\t// so the user can decide what to do with the results\n\t\t// TODO: Switch to callback within core to avoid isCancel Fn\n\t\tif (typeof opts?.onCancel === 'function' && isCancel(result)) {\n\t\t\tresults[name] = 'canceled';\n\t\t\topts.onCancel({ results });\n\t\t\tcontinue;\n\t\t}\n\n\t\tresults[name] = result;\n\t}\n\n\treturn results;\n};\n\nexport type Task = {\n\t/**\n\t * Task title\n\t */\n\ttitle: string;\n\t/**\n\t * Task function\n\t */\n\ttask: (message: (string: string) => void) => string | Promise<string> | void | Promise<void>;\n\n\t/**\n\t * If enabled === false the task will be skipped\n\t */\n\tenabled?: boolean;\n};\n\n/**\n * Define a group of tasks to be executed\n */\nexport const tasks = async (tasks: Task[]) => {\n\tfor (const task of tasks) {\n\t\tif (task.enabled === false) continue;\n\n\t\tconst s = spinner();\n\t\ts.start(task.title);\n\t\tconst result = await task.task(s.message);\n\t\ts.stop(result || task.title);\n\t}\n};\n"], "names": ["isUnicodeSupported", "process", "unicode", "s", "c", "fallback", "S_STEP_ACTIVE", "S_STEP_CANCEL", "S_STEP_ERROR", "S_STEP_SUBMIT", "S_BAR_START", "S_BAR", "S_BAR_END", "S_RADIO_ACTIVE", "S_RADIO_INACTIVE", "S_CHECKBOX_ACTIVE", "S_CHECKBOX_SELECTED", "S_CHECKBOX_INACTIVE", "S_PASSWORD_MASK", "S_BAR_H", "S_CORNER_TOP_RIGHT", "S_CONNECT_LEFT", "S_CORNER_BOTTOM_RIGHT", "S_INFO", "S_SUCCESS", "S_WARN", "S_ERROR", "symbol", "state", "color", "limitOptions", "params", "cursor", "options", "style", "paramMaxItems", "outputMaxItems", "maxItems", "slidingWindowLocation", "shouldRenderTopEllipsis", "shouldRenderBottomEllipsis", "option", "i", "arr", "isTopLimit", "isBottomLimit", "text", "opts", "TextPrompt", "title", "placeholder", "value", "password", "PasswordPrompt", "masked", "confirm", "active", "inactive", "ConfirmPrompt", "select", "opt", "label", "SelectPrompt", "item", "<PERSON><PERSON><PERSON>", "SelectKeyPrompt", "multiselect", "MultiSelectPrompt", "selected", "styleOption", "footer", "ln", "groupMultiselect", "selectableGroups", "isItem", "next", "isLast", "prefix", "selectedCheckbox", "unselectedCheckbox", "GroupMultiSelectPrompt", "note", "message", "lines", "titleLen", "strip", "len", "sum", "line", "msg", "cancel", "intro", "outro", "log", "parts", "firstLine", "stream", "iterable", "lineWidth", "chunk", "chunkLen", "spinner", "indicator", "frames", "delay", "isCI", "unblock", "loop", "isSpinnerActive", "_message", "_prevMessage", "_origin", "handleExit", "code", "stop", "errorEventHandler", "signalEventHandler", "registerHooks", "clearHooks", "clearPrevMessage", "prevLines", "erase", "parseMessage", "formatTimer", "origin", "duration", "min", "secs", "start", "block", "frameIndex", "indicatorTimer", "frame", "loadingDots", "step", "group", "prompts", "results", "prompt<PERSON><PERSON>s", "name", "prompt", "result", "e", "isCancel", "tasks", "task"], "mappings": "sZAEe,SAASA,IAAqB,CAC5C,OAAIC,EAAQ,WAAa,QACjBA,EAAQ,IAAI,OAAS,QAGtB,EAAQA,EAAQ,IAAI,IACvB,EAAQA,EAAQ,IAAI,YACpB,EAAQA,EAAQ,IAAI,kBACpBA,EAAQ,IAAI,aAAe,gBAC3BA,EAAQ,IAAI,eAAiB,oBAC7BA,EAAQ,IAAI,eAAiB,UAC7BA,EAAQ,IAAI,OAAS,kBACrBA,EAAQ,IAAI,OAAS,aACrBA,EAAQ,IAAI,oBAAsB,oBACvC,CCIA,MAAMC,EAAUF,KACVG,EAAI,CAACC,EAAWC,IAAsBH,EAAUE,EAAIC,EACpDC,GAAgBH,EAAE,SAAK,GAAG,EAC1BI,EAAgBJ,EAAE,SAAK,GAAG,EAC1BK,EAAeL,EAAE,SAAK,GAAG,EACzBM,EAAgBN,EAAE,SAAK,GAAG,EAE1BO,GAAcP,EAAE,SAAK,GAAG,EACxBQ,EAAQR,EAAE,SAAK,GAAG,EAClBS,EAAYT,EAAE,SAAK,QAAG,EAEtBU,EAAiBV,EAAE,SAAK,GAAG,EAC3BW,EAAmBX,EAAE,SAAK,GAAG,EAC7BY,EAAoBZ,EAAE,SAAK,UAAK,EAChCa,EAAsBb,EAAE,SAAK,KAAK,EAClCc,EAAsBd,EAAE,SAAK,KAAK,EAClCe,GAAkBf,EAAE,SAAK,QAAG,EAE5BgB,EAAUhB,EAAE,SAAK,GAAG,EACpBiB,GAAqBjB,EAAE,SAAK,GAAG,EAC/BkB,GAAiBlB,EAAE,SAAK,GAAG,EAC3BmB,GAAwBnB,EAAE,SAAK,GAAG,EAElCoB,EAASpB,EAAE,SAAK,QAAG,EACnBqB,EAAYrB,EAAE,SAAK,GAAG,EACtBsB,EAAStB,EAAE,SAAK,GAAG,EACnBuB,EAAUvB,EAAE,SAAK,GAAG,EAEpBwB,EAAUC,GAAiB,CAChC,OAAQA,EACP,CAAA,IAAK,UACL,IAAK,SACJ,OAAOC,EAAM,KAAKvB,EAAa,EAChC,IAAK,SACJ,OAAOuB,EAAM,IAAItB,CAAa,EAC/B,IAAK,QACJ,OAAOsB,EAAM,OAAOrB,CAAY,EACjC,IAAK,SACJ,OAAOqB,EAAM,MAAMpB,CAAa,CAClC,CACD,EASMqB,EAAyBC,GAAkD,CAChF,KAAM,CAAE,OAAAC,EAAQ,QAAAC,EAAS,MAAAC,CAAM,EAAIH,EAE7BI,EAAgBJ,EAAO,UAAY,OAAO,kBAC1CK,EAAiB,KAAK,IAAI,QAAQ,OAAO,KAAO,EAAG,CAAC,EAEpDC,EAAW,KAAK,IAAID,EAAgB,KAAK,IAAID,EAAe,CAAC,CAAC,EACpE,IAAIG,EAAwB,EAExBN,GAAUM,EAAwBD,EAAW,EAChDC,EAAwB,KAAK,IAAI,KAAK,IAAIN,EAASK,EAAW,EAAGJ,EAAQ,OAASI,CAAQ,EAAG,CAAC,EACpFL,EAASM,EAAwB,IAC3CA,EAAwB,KAAK,IAAIN,EAAS,EAAG,CAAC,GAG/C,MAAMO,EAA0BF,EAAWJ,EAAQ,QAAUK,EAAwB,EAC/EE,EACLH,EAAWJ,EAAQ,QAAUK,EAAwBD,EAAWJ,EAAQ,OAEzE,OAAOA,EACL,MAAMK,EAAuBA,EAAwBD,CAAQ,EAC7D,IAAI,CAACI,EAAQC,EAAGC,IAAQ,CACxB,MAAMC,EAAaF,IAAM,GAAKH,EACxBM,EAAgBH,IAAMC,EAAI,OAAS,GAAKH,EAC9C,OAAOI,GAAcC,EAClBhB,EAAM,IAAI,KAAK,EACfK,EAAMO,EAAQC,EAAIJ,IAA0BN,CAAM,CACtD,CAAC,CACH,EASac,GAAQC,GACb,IAAIC,EAAW,CACrB,SAAUD,EAAK,SACf,YAAaA,EAAK,YAClB,aAAcA,EAAK,aACnB,aAAcA,EAAK,aACnB,QAAS,CACR,MAAME,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EACpEG,EAAcH,EAAK,YACtBlB,EAAM,QAAQkB,EAAK,YAAY,CAAC,CAAC,EAAIlB,EAAM,IAAIkB,EAAK,YAAY,MAAM,CAAC,CAAC,EACxElB,EAAM,QAAQA,EAAM,OAAO,GAAG,CAAC,EAC5BsB,EAAS,KAAK,MAAsB,KAAK,gBAAnBD,EAE5B,OAAQ,KAAK,OACZ,IAAK,QACJ,MAAO,GAAGD,EAAM,MAAM;AAAA,EAAKpB,EAAM,OAAOlB,CAAK,CAAC,KAAKwC,CAAK;AAAA,EAAKtB,EAAM,OAClEjB,CACD,CAAC,KAAKiB,EAAM,OAAO,KAAK,KAAK,CAAC;AAAA,EAC/B,IAAK,SACJ,MAAO,GAAGoB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,IAAI,KAAK,OAASkB,EAAK,WAAW,CAAC,GAClF,IAAK,SACJ,MAAO,GAAGE,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,cAC7CA,EAAM,IAAI,KAAK,OAAS,EAAE,CAC3B,CAAC,GAAG,KAAK,OAAO,OAAS;AAAA,EAAKA,EAAM,KAAKlB,CAAK,CAAC,GAAK,EAAE,GACvD,QACC,MAAO,GAAGsC,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKwC,CAAK;AAAA,EAAKtB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CACzE,CACD,CACD,CAAC,EAAE,OAAA,EAQSwC,GAAYL,GACjB,IAAIM,EAAe,CACzB,SAAUN,EAAK,SACf,KAAMA,EAAK,MAAQ7B,GACnB,QAAS,CACR,MAAM+B,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EACpEI,EAAQ,KAAK,gBACbG,EAAS,KAAK,OAEpB,OAAQ,KAAK,MAAO,CACnB,IAAK,QACJ,MAAO,GAAGL,EAAM,MAAM;AAAA,EAAKpB,EAAM,OAAOlB,CAAK,CAAC,KAAK2C,CAAM;AAAA,EAAKzB,EAAM,OACnEjB,CACD,CAAC,KAAKiB,EAAM,OAAO,KAAK,KAAK,CAAC;AAAA,EAC/B,IAAK,SACJ,MAAO,GAAGoB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,IAAIyB,CAAM,CAAC,GAC1D,IAAK,SACJ,MAAO,GAAGL,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,cAAcA,EAAM,IAAIyB,GAAU,EAAE,CAAC,CAAC,GACnFA,EAAS;AAAA,EAAKzB,EAAM,KAAKlB,CAAK,CAAC,GAAK,EACrC,GACD,QACC,MAAO,GAAGsC,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKwC,CAAK;AAAA,EAAKtB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CACzE,CACD,CACD,CAAC,EAAE,SASS2C,GAAWR,GAAyB,CAChD,MAAMS,EAAST,EAAK,QAAU,MACxBU,EAAWV,EAAK,UAAY,KAClC,OAAO,IAAIW,EAAc,CACxB,OAAAF,EACA,SAAAC,EACA,aAAcV,EAAK,cAAgB,GACnC,QAAS,CACR,MAAME,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EACpEI,EAAQ,KAAK,MAAQK,EAASC,EAEpC,OAAQ,KAAK,MACZ,CAAA,IAAK,SACJ,MAAO,GAAGR,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,IAAIsB,CAAK,CAAC,GACzD,IAAK,SACJ,MAAO,GAAGF,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,cAC7CA,EAAM,IAAIsB,CAAK,CAChB,CAAC;AAAA,EAAKtB,EAAM,KAAKlB,CAAK,CAAC,GACxB,QACC,MAAO,GAAGsC,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAClC,KAAK,MACF,GAAGkB,EAAM,MAAMhB,CAAc,CAAC,IAAI2C,CAAM,GACxC,GAAG3B,EAAM,IAAIf,CAAgB,CAAC,IAAIe,EAAM,IAAI2B,CAAM,CAAC,EACvD,IAAI3B,EAAM,IAAI,GAAG,CAAC,IAChB,KAAK,MAEH,GAAGA,EAAM,IAAIf,CAAgB,CAAC,IAAIe,EAAM,IAAI4B,CAAQ,CAAC,GADrD,GAAG5B,EAAM,MAAMhB,CAAc,CAAC,IAAI4C,CAAQ,EAE9C;AAAA,EAAK5B,EAAM,KAAKjB,CAAS,CAAC;AAAA,CAE5B,CACD,CACD,CAAC,EAAE,QACJ,EAiDa+C,GAAiBZ,GAA+B,CAC5D,MAAMa,EAAM,CAACnB,EAAuBb,IAA4D,CAC/F,MAAMiC,EAAQpB,EAAO,OAAS,OAAOA,EAAO,KAAK,EACjD,OAAQb,GACP,IAAK,WACJ,MAAO,GAAGC,EAAM,IAAIgC,CAAK,CAAC,GAC3B,IAAK,SACJ,MAAO,GAAGhC,EAAM,MAAMhB,CAAc,CAAC,IAAIgD,CAAK,IAC7CpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GACD,IAAK,YACJ,MAAO,GAAGZ,EAAM,cAAcA,EAAM,IAAIgC,CAAK,CAAC,CAAC,GAChD,QACC,MAAO,GAAGhC,EAAM,IAAIf,CAAgB,CAAC,IAAIe,EAAM,IAAIgC,CAAK,CAAC,EAC3D,CACD,EAEA,OAAO,IAAIC,GAAa,CACvB,QAASf,EAAK,QACd,aAAcA,EAAK,aACnB,QAAS,CACR,MAAME,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EAE1E,OAAQ,KAAK,MACZ,CAAA,IAAK,SACJ,MAAO,GAAGE,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKiD,EAAI,KAAK,QAAQ,KAAK,MAAM,EAAG,UAAU,CAAC,GACnF,IAAK,SACJ,MAAO,GAAGX,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKiD,EACvC,KAAK,QAAQ,KAAK,MAAM,EACxB,WACD,CAAC;AAAA,EAAK/B,EAAM,KAAKlB,CAAK,CAAC,GACxB,QACC,MAAO,GAAGsC,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKmB,EAAa,CACpD,OAAQ,KAAK,OACb,QAAS,KAAK,QACd,SAAUiB,EAAK,SACf,MAAO,CAACgB,EAAMP,IAAWI,EAAIG,EAAMP,EAAS,SAAW,UAAU,CAClE,CAAC,EAAE,KAAK;AAAA,EAAK3B,EAAM,KAAKlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAKkB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CAE/D,CACD,CACD,CAAC,EAAE,OAAA,CACJ,EAEaoD,GAAmCjB,GAA+B,CAC9E,MAAMa,EAAM,CACXnB,EACAb,EAA0D,aACtD,CACJ,MAAMiC,EAAQpB,EAAO,OAAS,OAAOA,EAAO,KAAK,EACjD,OAAIb,IAAU,WACN,GAAGC,EAAM,IAAIgC,CAAK,CAAC,GAEvBjC,IAAU,YACN,GAAGC,EAAM,cAAcA,EAAM,IAAIgC,CAAK,CAAC,CAAC,GAE5CjC,IAAU,SACN,GAAGC,EAAM,OAAOA,EAAM,KAAK,IAAIY,EAAO,KAAK,GAAG,CAAC,CAAC,IAAIoB,CAAK,IAC/DpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAEM,GAAGZ,EAAM,KAAKA,EAAM,QAAQA,EAAM,QAAQ,IAAIY,EAAO,KAAK,GAAG,CAAC,CAAC,CAAC,IAAIoB,CAAK,IAC/EpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,EACD,EAEA,OAAO,IAAIwB,GAAgB,CAC1B,QAASlB,EAAK,QACd,aAAcA,EAAK,aACnB,QAAS,CACR,MAAME,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EAE1E,OAAQ,KAAK,MAAO,CACnB,IAAK,SACJ,MAAO,GAAGE,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKiD,EACvC,KAAK,QAAQ,KAAMA,GAAQA,EAAI,QAAU,KAAK,KAAK,GAAKb,EAAK,QAAQ,CAAC,EACtE,UACD,CAAC,GACF,IAAK,SACJ,MAAO,GAAGE,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKiD,EAAI,KAAK,QAAQ,CAAC,EAAG,WAAW,CAAC;AAAA,EAAK/B,EAAM,KACnFlB,CACD,CAAC,GACF,QACC,MAAO,GAAGsC,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAK,KAAK,QAC3C,IAAI,CAAC8B,EAAQC,IAAMkB,EAAInB,EAAQC,IAAM,KAAK,OAAS,SAAW,UAAU,CAAC,EACzE,KAAK;AAAA,EAAKb,EAAM,KAAKlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAKkB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CAE9D,CACD,CACD,CAAC,EAAE,OACJ,CAAA,EAUasD,GAAsBnB,GAAoC,CACtE,MAAMa,EAAM,CACXnB,EACAb,IACI,CACJ,MAAMiC,EAAQpB,EAAO,OAAS,OAAOA,EAAO,KAAK,EACjD,OAAIb,IAAU,SACN,GAAGC,EAAM,KAAKd,CAAiB,CAAC,IAAI8C,CAAK,IAC/CpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAEGb,IAAU,WACN,GAAGC,EAAM,MAAMb,CAAmB,CAAC,IAAIa,EAAM,IAAIgC,CAAK,CAAC,IAC7DpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAEGb,IAAU,YACN,GAAGC,EAAM,cAAcA,EAAM,IAAIgC,CAAK,CAAC,CAAC,GAE5CjC,IAAU,kBACN,GAAGC,EAAM,MAAMb,CAAmB,CAAC,IAAI6C,CAAK,IAClDpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAEGb,IAAU,YACN,GAAGC,EAAM,IAAIgC,CAAK,CAAC,GAEpB,GAAGhC,EAAM,IAAIZ,CAAmB,CAAC,IAAIY,EAAM,IAAIgC,CAAK,CAAC,EAC7D,EAEA,OAAO,IAAIM,GAAkB,CAC5B,QAASpB,EAAK,QACd,cAAeA,EAAK,cACpB,SAAUA,EAAK,UAAY,GAC3B,SAAUA,EAAK,SACf,SAASqB,EAAmB,CAC3B,GAAI,KAAK,UAAYA,EAAS,SAAW,EACxC,MAAO;AAAA,EAAuCvC,EAAM,MACnDA,EAAM,IACL,SAASA,EAAM,KAAKA,EAAM,QAAQA,EAAM,QAAQ,SAAS,CAAC,CAAC,CAAC,eAAeA,EAAM,KAChFA,EAAM,QAAQA,EAAM,QAAQ,SAAS,CAAC,CACvC,CAAC,YACF,CACD,CAAC,EACH,EACA,QAAS,CACR,MAAMoB,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EAEpEsB,EAAc,CAAC5B,EAAuBe,IAAoB,CAC/D,MAAMY,EAAW,KAAK,MAAM,SAAS3B,EAAO,KAAK,EACjD,OAAIe,GAAUY,EACNR,EAAInB,EAAQ,iBAAiB,EAEjC2B,EACIR,EAAInB,EAAQ,UAAU,EAEvBmB,EAAInB,EAAQe,EAAS,SAAW,UAAU,CAClD,EAEA,OAAQ,KAAK,MACZ,CAAA,IAAK,SACJ,MAAO,GAAGP,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAClC,KAAK,QACH,OAAO,CAAC,CAAE,MAAAwC,CAAM,IAAM,KAAK,MAAM,SAASA,CAAK,CAAC,EAChD,IAAKV,GAAWmB,EAAInB,EAAQ,WAAW,CAAC,EACxC,KAAKZ,EAAM,IAAI,IAAI,CAAC,GAAKA,EAAM,IAAI,MAAM,CAC5C,GAED,IAAK,SAAU,CACd,MAAMgC,EAAQ,KAAK,QACjB,OAAO,CAAC,CAAE,MAAAV,CAAM,IAAM,KAAK,MAAM,SAASA,CAAK,CAAC,EAChD,IAAKV,GAAWmB,EAAInB,EAAQ,WAAW,CAAC,EACxC,KAAKZ,EAAM,IAAI,IAAI,CAAC,EACtB,MAAO,GAAGoB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAClCkD,EAAM,OAAS,GAAGA,CAAK;AAAA,EAAKhC,EAAM,KAAKlB,CAAK,CAAC,GAAK,EACnD,EACD,CACA,IAAK,QAAS,CACb,MAAM2D,EAAS,KAAK,MAClB,MAAM;AAAA,CAAI,EACV,IAAI,CAACC,EAAI7B,IACTA,IAAM,EAAI,GAAGb,EAAM,OAAOjB,CAAS,CAAC,KAAKiB,EAAM,OAAO0C,CAAE,CAAC,GAAK,MAAMA,CAAE,EACvE,EACC,KAAK;AAAA,CAAI,EACX,MAAO,GAAGtB,EAAQpB,EAAM,OAAOlB,CAAK,CAAC,KAAKmB,EAAa,CACtD,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,SAAUiB,EAAK,SACf,MAAOsB,CACR,CAAC,EAAE,KAAK;AAAA,EAAKxC,EAAM,OAAOlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAK2D,CAAM;AAAA,CACjD,CACA,QACC,MAAO,GAAGrB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAKmB,EAAa,CACpD,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,SAAUiB,EAAK,SACf,MAAOsB,CACR,CAAC,EAAE,KAAK;AAAA,EAAKxC,EAAM,KAAKlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAKkB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CAE/D,CACD,CACD,CAAC,EAAE,OAAO,CACX,EAUa4D,GAA2BzB,GAAyC,CAChF,KAAM,CAAE,iBAAA0B,EAAmB,EAAK,EAAI1B,EAC9Ba,EAAM,CACXnB,EACAb,EASAK,EAA2B,CACvB,IAAA,CACJ,MAAM4B,EAAQpB,EAAO,OAAS,OAAOA,EAAO,KAAK,EAC3CiC,EAAS,OAAQjC,EAAe,OAAU,SAC1CkC,EAAOD,IAAWzC,EAAQA,EAAQ,QAAQQ,CAAM,EAAI,CAAC,GAAK,CAAE,MAAO,EAAK,GACxEmC,EAASF,GAAWC,EAAa,QAAU,GAC3CE,EAASH,EAAUD,EAAmB,GAAGG,EAAShE,EAAYD,CAAK,IAAM,KAAQ,GAEvF,GAAIiB,IAAU,SACb,MAAO,GAAGC,EAAM,IAAIgD,CAAM,CAAC,GAAGhD,EAAM,KAAKd,CAAiB,CAAC,IAAI8C,CAAK,IACnEpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAED,GAAIb,IAAU,eACb,MAAO,GAAGiD,CAAM,GAAGhD,EAAM,KAAKd,CAAiB,CAAC,IAAIc,EAAM,IAAIgC,CAAK,CAAC,GAErE,GAAIjC,IAAU,wBACb,MAAO,GAAGiD,CAAM,GAAGhD,EAAM,MAAMb,CAAmB,CAAC,IAAIa,EAAM,IAAIgC,CAAK,CAAC,GAExE,GAAIjC,IAAU,WAAY,CACzB,MAAMkD,EAAmBJ,GAAUD,EAAmB5C,EAAM,MAAMb,CAAmB,EAAI,GACzF,MAAO,GAAGa,EAAM,IAAIgD,CAAM,CAAC,GAAGC,CAAgB,IAAIjD,EAAM,IAAIgC,CAAK,CAAC,IACjEpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,EACD,CACA,GAAIb,IAAU,YACb,MAAO,GAAGC,EAAM,cAAcA,EAAM,IAAIgC,CAAK,CAAC,CAAC,GAEhD,GAAIjC,IAAU,kBACb,MAAO,GAAGC,EAAM,IAAIgD,CAAM,CAAC,GAAGhD,EAAM,MAAMb,CAAmB,CAAC,IAAI6C,CAAK,IACtEpB,EAAO,KAAOZ,EAAM,IAAI,IAAIY,EAAO,IAAI,GAAG,EAAI,EAC/C,GAED,GAAIb,IAAU,YACb,MAAO,GAAGC,EAAM,IAAIgC,CAAK,CAAC,GAE3B,MAAMkB,EAAqBL,GAAUD,EAAmB5C,EAAM,IAAIZ,CAAmB,EAAI,GACzF,MAAO,GAAGY,EAAM,IAAIgD,CAAM,CAAC,GAAGE,CAAkB,IAAIlD,EAAM,IAAIgC,CAAK,CAAC,EACrE,EAEA,OAAO,IAAImB,GAAuB,CACjC,QAASjC,EAAK,QACd,cAAeA,EAAK,cACpB,SAAUA,EAAK,UAAY,GAC3B,SAAUA,EAAK,SACf,iBAAA0B,EACA,SAASL,EAAmB,CAC3B,GAAI,KAAK,UAAYA,EAAS,SAAW,EACxC,MAAO;AAAA,EAAuCvC,EAAM,MACnDA,EAAM,IACL,SAASA,EAAM,KAAKA,EAAM,QAAQA,EAAM,QAAQ,SAAS,CAAC,CAAC,CAAC,eAAeA,EAAM,KAChFA,EAAM,QAAQA,EAAM,QAAQ,SAAS,CAAC,CACvC,CAAC,YACF,CACD,CAAC,EACH,EACA,QAAS,CACR,MAAMoB,EAAQ,GAAGpB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,EAAO,KAAK,KAAK,CAAC,KAAKoB,EAAK,OAAO;AAAA,EAE1E,OAAQ,KAAK,MACZ,CAAA,IAAK,SACJ,MAAO,GAAGE,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAK,KAAK,QAC3C,OAAO,CAAC,CAAE,MAAAwC,CAAM,IAAM,KAAK,MAAM,SAASA,CAAK,CAAC,EAChD,IAAKV,GAAWmB,EAAInB,EAAQ,WAAW,CAAC,EACxC,KAAKZ,EAAM,IAAI,IAAI,CAAC,CAAC,GAExB,IAAK,SAAU,CACd,MAAMgC,EAAQ,KAAK,QACjB,OAAO,CAAC,CAAE,MAAAV,CAAM,IAAM,KAAK,MAAM,SAASA,CAAK,CAAC,EAChD,IAAKV,GAAWmB,EAAInB,EAAQ,WAAW,CAAC,EACxC,KAAKZ,EAAM,IAAI,IAAI,CAAC,EACtB,MAAO,GAAGoB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAClCkD,EAAM,KAAK,EAAI,GAAGA,CAAK;AAAA,EAAKhC,EAAM,KAAKlB,CAAK,CAAC,GAAK,EACnD,EACD,CACA,IAAK,QAAS,CACb,MAAM2D,EAAS,KAAK,MAClB,MAAM;AAAA,CAAI,EACV,IAAI,CAACC,EAAI7B,IACTA,IAAM,EAAI,GAAGb,EAAM,OAAOjB,CAAS,CAAC,KAAKiB,EAAM,OAAO0C,CAAE,CAAC,GAAK,MAAMA,CAAE,EACvE,EACC,KAAK;AAAA,CAAI,EACX,MAAO,GAAGtB,CAAK,GAAGpB,EAAM,OAAOlB,CAAK,CAAC,KAAK,KAAK,QAC7C,IAAI,CAAC8B,EAAQC,EAAGT,IAAY,CAC5B,MAAMmC,EACL,KAAK,MAAM,SAAS3B,EAAO,KAAK,GAC/BA,EAAO,QAAU,IAAQ,KAAK,gBAAgB,GAAGA,EAAO,KAAK,EAAE,EAC3De,EAASd,IAAM,KAAK,OAK1B,MAHC,CAACc,GACD,OAAOf,EAAO,OAAU,UACxB,KAAK,QAAQ,KAAK,MAAM,EAAE,QAAUA,EAAO,MAEpCmB,EAAInB,EAAQ2B,EAAW,wBAA0B,eAAgBnC,CAAO,EAE5EuB,GAAUY,EACNR,EAAInB,EAAQ,kBAAmBR,CAAO,EAE1CmC,EACIR,EAAInB,EAAQ,WAAYR,CAAO,EAEhC2B,EAAInB,EAAQe,EAAS,SAAW,WAAYvB,CAAO,CAC3D,CAAC,EACA,KAAK;AAAA,EAAKJ,EAAM,OAAOlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAK2D,CAAM;AAAA,CAChD,CACA,QACC,MAAO,GAAGrB,CAAK,GAAGpB,EAAM,KAAKlB,CAAK,CAAC,KAAK,KAAK,QAC3C,IAAI,CAAC8B,EAAQC,EAAGT,IAAY,CAC5B,MAAMmC,EACL,KAAK,MAAM,SAAS3B,EAAO,KAAK,GAC/BA,EAAO,QAAU,IAAQ,KAAK,gBAAgB,GAAGA,EAAO,KAAK,EAAE,EAC3De,EAASd,IAAM,KAAK,OAK1B,MAHC,CAACc,GACD,OAAOf,EAAO,OAAU,UACxB,KAAK,QAAQ,KAAK,MAAM,EAAE,QAAUA,EAAO,MAEpCmB,EAAInB,EAAQ2B,EAAW,wBAA0B,eAAgBnC,CAAO,EAE5EuB,GAAUY,EACNR,EAAInB,EAAQ,kBAAmBR,CAAO,EAE1CmC,EACIR,EAAInB,EAAQ,WAAYR,CAAO,EAEhC2B,EAAInB,EAAQe,EAAS,SAAW,WAAYvB,CAAO,CAC3D,CAAC,EACA,KAAK;AAAA,EAAKJ,EAAM,KAAKlB,CAAK,CAAC,IAAI,CAAC;AAAA,EAAKkB,EAAM,KAAKjB,CAAS,CAAC;AAAA,CAE9D,CACD,CACD,CAAC,EAAE,OACJ,CAAA,EAEaqE,GAAO,CAACC,EAAU,GAAIjC,EAAQ,KAAO,CACjD,MAAMkC,EAAQ;AAAA,EAAKD,CAAO;AAAA,EAAK,MAAM;AAAA,CAAI,EACnCE,EAAWC,EAAMpC,CAAK,EAAE,OACxBqC,EACL,KAAK,IACJH,EAAM,OAAO,CAACI,EAAKhB,IAAO,CACzB,MAAMiB,EAAOH,EAAMd,CAAE,EACrB,OAAOiB,EAAK,OAASD,EAAMC,EAAK,OAASD,CAC1C,EAAG,CAAC,EACJH,CACD,EAAI,EACCK,EAAMN,EACV,IACCZ,GACA,GAAG1C,EAAM,KAAKlB,CAAK,CAAC,KAAKkB,EAAM,IAAI0C,CAAE,CAAC,GAAG,IAAI,OAAOe,EAAMD,EAAMd,CAAE,EAAE,MAAM,CAAC,GAAG1C,EAAM,KACnFlB,CACD,CAAC,EACH,EACC,KAAK;AAAA,CAAI,EACX,QAAQ,OAAO,MACd,GAAGkB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKkB,EAAM,MAAMpB,CAAa,CAAC,KAAKoB,EAAM,MAAMoB,CAAK,CAAC,IAAIpB,EAAM,KACnFV,EAAQ,OAAO,KAAK,IAAImE,EAAMF,EAAW,EAAG,CAAC,CAAC,EAAIhE,EACnD,CAAC;AAAA,EAAKqE,CAAG;AAAA,EAAK5D,EAAM,KAAKR,GAAiBF,EAAQ,OAAOmE,EAAM,CAAC,EAAIhE,EAAqB,CAAC;AAAA,CAC3F,CACD,EAEaoE,GAAS,CAACR,EAAU,KAAO,CACvC,QAAQ,OAAO,MAAM,GAAGrD,EAAM,KAAKjB,CAAS,CAAC,KAAKiB,EAAM,IAAIqD,CAAO,CAAC;AAAA;AAAA,CAAM,CAC3E,EAEaS,GAAQ,CAAC1C,EAAQ,KAAO,CACpC,QAAQ,OAAO,MAAM,GAAGpB,EAAM,KAAKnB,EAAW,CAAC,KAAKuC,CAAK;AAAA,CAAI,CAC9D,EAEa2C,GAAQ,CAACV,EAAU,KAAO,CACtC,QAAQ,OAAO,MAAM,GAAGrD,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKkB,EAAM,KAAKjB,CAAS,CAAC,KAAKsE,CAAO;AAAA;AAAA,CAAM,CACtF,EAKaW,EAAM,CAClB,QAAS,CAACX,EAAU,GAAI,CAAE,OAAAvD,EAASE,EAAM,KAAKlB,CAAK,CAAE,EAAuB,CAAO,IAAA,CAClF,MAAMmF,EAAQ,CAAC,GAAGjE,EAAM,KAAKlB,CAAK,CAAC,EAAE,EACrC,GAAIuE,EAAS,CACZ,KAAM,CAACa,EAAW,GAAGZ,CAAK,EAAID,EAAQ,MAAM;AAAA,CAAI,EAChDY,EAAM,KAAK,GAAGnE,CAAM,KAAKoE,CAAS,GAAI,GAAGZ,EAAM,IAAKZ,GAAO,GAAG1C,EAAM,KAAKlB,CAAK,CAAC,KAAK4D,CAAE,EAAE,CAAC,CAC1F,CACA,QAAQ,OAAO,MAAM,GAAGuB,EAAM,KAAK;AAAA,CAAI,CAAC;AAAA,CAAI,CAC7C,EACA,KAAOZ,GAAoB,CAC1BW,EAAI,QAAQX,EAAS,CAAE,OAAQrD,EAAM,KAAKN,CAAM,CAAE,CAAC,CACpD,EACA,QAAU2D,GAAoB,CAC7BW,EAAI,QAAQX,EAAS,CAAE,OAAQrD,EAAM,MAAML,CAAS,CAAE,CAAC,CACxD,EACA,KAAO0D,GAAoB,CAC1BW,EAAI,QAAQX,EAAS,CAAE,OAAQrD,EAAM,MAAMpB,CAAa,CAAE,CAAC,CAC5D,EACA,KAAOyE,GAAoB,CAC1BW,EAAI,QAAQX,EAAS,CAAE,OAAQrD,EAAM,OAAOJ,CAAM,CAAE,CAAC,CACtD,EAEA,QAAUyD,GAAoB,CAC7BW,EAAI,KAAKX,CAAO,CACjB,EACA,MAAQA,GAAoB,CAC3BW,EAAI,QAAQX,EAAS,CAAE,OAAQrD,EAAM,IAAIH,CAAO,CAAE,CAAC,CACpD,CACD,EAEMmD,EAAS,GAAGhD,EAAM,KAAKlB,CAAK,CAAC,KACtBqF,EAAS,CACrB,QAAS,MACRC,EACA,CAAE,OAAAtE,EAASE,EAAM,KAAKlB,CAAK,CAAE,EAAuB,KAChD,CACJ,QAAQ,OAAO,MAAM,GAAGkB,EAAM,KAAKlB,CAAK,CAAC;AAAA,EAAKgB,CAAM,IAAI,EACxD,IAAIuE,EAAY,EAChB,cAAeC,KAASF,EAAU,CACjCE,EAAQA,EAAM,QAAQ,MAAO;AAAA,EAAKtB,CAAM,EAAE,EACtCsB,EAAM,SAAS;AAAA,CAAI,IACtBD,EAAY,EAAIb,EAAMc,EAAM,MAAMA,EAAM,YAAY;AAAA,CAAI,CAAC,CAAC,EAAE,QAE7D,MAAMC,EAAWf,EAAMc,CAAK,EAAE,OAC1BD,EAAYE,EAAW,QAAQ,OAAO,SACzCF,GAAaE,EACb,QAAQ,OAAO,MAAMD,CAAK,IAE1B,QAAQ,OAAO,MAAM;AAAA,EAAKtB,CAAM,GAAGsB,EAAM,WAAW,EAAE,EACtDD,EAAY,EAAIb,EAAMc,EAAM,UAAW,CAAA,EAAE,OAE3C,CACA,QAAQ,OAAO,MAAM;AAAA,CAAI,CAC1B,EACA,KAAOF,GACCD,EAAO,QAAQC,EAAU,CAAE,OAAQpE,EAAM,KAAKN,CAAM,CAAE,CAAC,EAE/D,QAAU0E,GACFD,EAAO,QAAQC,EAAU,CAAE,OAAQpE,EAAM,MAAML,CAAS,CAAE,CAAC,EAEnE,KAAOyE,GACCD,EAAO,QAAQC,EAAU,CAAE,OAAQpE,EAAM,MAAMpB,CAAa,CAAE,CAAC,EAEvE,KAAOwF,GACCD,EAAO,QAAQC,EAAU,CAAE,OAAQpE,EAAM,OAAOJ,CAAM,CAAE,CAAC,EAGjE,QAAUwE,GACFD,EAAO,KAAKC,CAAQ,EAE5B,MAAQA,GACAD,EAAO,QAAQC,EAAU,CAAE,OAAQpE,EAAM,IAAIH,CAAO,CAAE,CAAC,CAEhE,EAMa2E,EAAU,CAAC,CAAE,UAAAC,EAAY,MAAO,EAAoB,CAAO,IAAA,CACvE,MAAMC,EAASrG,EAAU,CAAC,SAAK,SAAK,SAAK,QAAG,EAAI,CAAC,SAAK,IAAK,IAAK,GAAG,EAC7DsG,EAAQtG,EAAU,GAAK,IACvBuG,EAAO,QAAQ,IAAI,KAAO,OAEhC,IAAIC,EACAC,EACAC,EAAkB,GAClBC,EAAW,GACXC,EACAC,EAAkB,YAAY,MAElC,MAAMC,EAAcC,GAAiB,CACpC,MAAMxB,EAAMwB,EAAO,EAAI,uBAAyB,WAC5CL,GAAiBM,EAAKzB,EAAKwB,CAAI,CACpC,EAEME,EAAoB,IAAMH,EAAW,CAAC,EACtCI,EAAqB,IAAMJ,EAAW,CAAC,EAEvCK,EAAgB,IAAM,CAE3B,QAAQ,GAAG,2BAA4BF,CAAiB,EAExD,QAAQ,GAAG,qBAAsBA,CAAiB,EAElD,QAAQ,GAAG,SAAUC,CAAkB,EACvC,QAAQ,GAAG,UAAWA,CAAkB,EACxC,QAAQ,GAAG,OAAQJ,CAAU,CAC9B,EAEMM,EAAa,IAAM,CACxB,QAAQ,eAAe,2BAA4BH,CAAiB,EACpE,QAAQ,eAAe,qBAAsBA,CAAiB,EAC9D,QAAQ,eAAe,SAAUC,CAAkB,EACnD,QAAQ,eAAe,UAAWA,CAAkB,EACpD,QAAQ,eAAe,OAAQJ,CAAU,CAC1C,EAEMO,EAAmB,IAAM,CAC9B,GAAIT,IAAiB,OAAW,OAC5BL,GAAM,QAAQ,OAAO,MAAM;AAAA,CAAI,EACnC,MAAMe,EAAYV,EAAa,MAAM;AAAA,CAAI,EACzC,QAAQ,OAAO,MAAM9E,GAAO,KAAK,KAAMwF,EAAU,OAAS,CAAC,CAAC,EAC5D,QAAQ,OAAO,MAAMC,GAAM,KAAKD,EAAU,MAAM,CAAC,CAClD,EAEME,EAAgBjC,GACdA,EAAI,QAAQ,OAAQ,EAAE,EAGxBkC,EAAeC,GAA2B,CAC/C,MAAMC,GAAY,YAAY,MAAQD,GAAU,IAC1CE,EAAM,KAAK,MAAMD,EAAW,EAAE,EAC9BE,EAAO,KAAK,MAAMF,EAAW,EAAE,EACrC,OAAOC,EAAM,EAAI,IAAIA,CAAG,KAAKC,CAAI,KAAO,IAAIA,CAAI,IACjD,EAEMC,EAAQ,CAACvC,EAAM,KAAa,CACjCmB,EAAkB,GAClBF,EAAUuB,GAAAA,EACVpB,EAAWa,EAAajC,CAAG,EAC3BsB,EAAU,YAAY,IAAI,EAC1B,QAAQ,OAAO,MAAM,GAAGlF,EAAM,KAAKlB,CAAK,CAAC;AAAA,CAAI,EAC7C,IAAIuH,EAAa,EACbC,EAAiB,EACrBd,IACAV,EAAO,YAAY,IAAM,CACxB,GAAIF,GAAQI,IAAaC,EACxB,OAEDS,IACAT,EAAeD,EACf,MAAMuB,EAAQvG,EAAM,QAAQ0E,EAAO2B,CAAU,CAAC,EAE9C,GAAIzB,EACH,QAAQ,OAAO,MAAM,GAAG2B,CAAK,KAAKvB,CAAQ,KAAK,UACrCP,IAAc,QACxB,QAAQ,OAAO,MAAM,GAAG8B,CAAK,KAAKvB,CAAQ,IAAIc,EAAYZ,CAAO,CAAC,EAAE,MAC9D,CACN,MAAMsB,EAAc,IAAI,OAAO,KAAK,MAAMF,CAAc,CAAC,EAAE,MAAM,EAAG,CAAC,EACrE,QAAQ,OAAO,MAAM,GAAGC,CAAK,KAAKvB,CAAQ,GAAGwB,CAAW,EAAE,CAC3D,CAEAH,EAAaA,EAAa,EAAI3B,EAAO,OAAS2B,EAAa,EAAI,EAC/DC,EAAiBA,EAAiB5B,EAAO,OAAS4B,EAAiB,KAAQ,CAC5E,EAAG3B,CAAK,CACT,EAEMU,EAAO,CAACzB,EAAM,GAAIwB,EAAO,IAAY,CAC1CL,EAAkB,GAClB,cAAcD,CAAI,EAClBY,EAAiB,EACjB,MAAMe,EACLrB,IAAS,EACNpF,EAAM,MAAMpB,CAAa,EACzBwG,IAAS,EACRpF,EAAM,IAAItB,CAAa,EACvBsB,EAAM,IAAIrB,CAAY,EAC3BqG,EAAWa,EAAajC,GAAOoB,CAAQ,EACnCP,IAAc,QACjB,QAAQ,OAAO,MAAM,GAAGgC,CAAI,KAAKzB,CAAQ,IAAIc,EAAYZ,CAAO,CAAC;AAAA,CAAI,EAErE,QAAQ,OAAO,MAAM,GAAGuB,CAAI,KAAKzB,CAAQ;AAAA,CAAI,EAE9CS,EAAAA,EACAZ,EAAAA,CACD,EAMA,MAAO,CACN,MAAAsB,EACA,KAAAd,EACA,QAPe,CAACzB,EAAM,KAAa,CACnCoB,EAAWa,EAAajC,GAAOoB,CAAQ,CACxC,CAMA,CACD,EA4Ba0B,GAAQ,MACpBC,EACAzF,IACoD,CACpD,MAAM0F,EAAU,CACVC,EAAAA,EAAc,OAAO,KAAKF,CAAO,EAEvC,UAAWG,KAAQD,EAAa,CAC/B,MAAME,EAASJ,EAAQG,CAAe,EAChCE,EAAS,MAAMD,EAAO,CAAE,QAAAH,CAAQ,CAAC,GAAG,MAAOK,GAAM,CACtD,MAAMA,CACP,CAAC,EAKD,GAAI,OAAO/F,GAAM,UAAa,YAAcgG,GAASF,CAAM,EAAG,CAC7DJ,EAAQE,CAAI,EAAI,WAChB5F,EAAK,SAAS,CAAE,QAAA0F,CAAQ,CAAC,EACzB,QACD,CAEAA,EAAQE,CAAI,EAAIE,CACjB,CAEA,OAAOJ,CACR,EAqBaO,GAAQ,MAAOA,GAAkB,CAC7C,UAAWC,KAAQD,EAAO,CACzB,GAAIC,EAAK,UAAY,GAAO,SAE5B,MAAM9I,EAAIkG,IACVlG,EAAE,MAAM8I,EAAK,KAAK,EAClB,MAAMJ,EAAS,MAAMI,EAAK,KAAK9I,EAAE,OAAO,EACxCA,EAAE,KAAK0I,GAAUI,EAAK,KAAK,CAC5B,CACD", "x_google_ignoreList": [0]}