"use strict";const sisteransi=require("sisteransi"),node_process=require("node:process"),s$2=require("node:readline"),node_stream=require("node:stream"),i$1=require("picocolors");function _interopDefaultCompat(C){return C&&typeof C=="object"&&"default"in C?C.default:C}function _interopNamespaceCompat(C){if(C&&typeof C=="object"&&"default"in C)return C;const t=Object.create(null);if(C)for(const F in C)t[F]=C[F];return t.default=C,t}const s__namespace=_interopNamespaceCompat(s$2),s__default=_interopDefaultCompat(s$2),i__default=_interopDefaultCompat(i$1);function ansiRegex({onlyFirst:C=!1}={}){const F=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(F,C?void 0:"g")}const regex=ansiRegex();function stripAnsi(C){if(typeof C!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof C}\``);return C.replace(regex,"")}function getDefaultExportFromCjs(C){return C&&C.__esModule&&Object.prototype.hasOwnProperty.call(C,"default")?C.default:C}var eastasianwidth={exports:{}};(function(C){var t={};C.exports=t,t.eastAsianWidth=function(E){var B=E.charCodeAt(0),A=E.length==2?E.charCodeAt(1):0,D=B;return 55296<=B&&B<=56319&&56320<=A&&A<=57343&&(B&=1023,A&=1023,D=B<<10|A,D+=65536),D==12288||65281<=D&&D<=65376||65504<=D&&D<=65510?"F":D==8361||65377<=D&&D<=65470||65474<=D&&D<=65479||65482<=D&&D<=65487||65490<=D&&D<=65495||65498<=D&&D<=65500||65512<=D&&D<=65518?"H":4352<=D&&D<=4447||4515<=D&&D<=4519||4602<=D&&D<=4607||9001<=D&&D<=9002||11904<=D&&D<=11929||11931<=D&&D<=12019||12032<=D&&D<=12245||12272<=D&&D<=12283||12289<=D&&D<=12350||12353<=D&&D<=12438||12441<=D&&D<=12543||12549<=D&&D<=12589||12593<=D&&D<=12686||12688<=D&&D<=12730||12736<=D&&D<=12771||12784<=D&&D<=12830||12832<=D&&D<=12871||12880<=D&&D<=13054||13056<=D&&D<=19903||19968<=D&&D<=42124||42128<=D&&D<=42182||43360<=D&&D<=43388||44032<=D&&D<=55203||55216<=D&&D<=55238||55243<=D&&D<=55291||63744<=D&&D<=64255||65040<=D&&D<=65049||65072<=D&&D<=65106||65108<=D&&D<=65126||65128<=D&&D<=65131||110592<=D&&D<=110593||127488<=D&&D<=127490||127504<=D&&D<=127546||127552<=D&&D<=127560||127568<=D&&D<=127569||131072<=D&&D<=194367||177984<=D&&D<=196605||196608<=D&&D<=262141?"W":32<=D&&D<=126||162<=D&&D<=163||165<=D&&D<=166||D==172||D==175||10214<=D&&D<=10221||10629<=D&&D<=10630?"Na":D==161||D==164||167<=D&&D<=168||D==170||173<=D&&D<=174||176<=D&&D<=180||182<=D&&D<=186||188<=D&&D<=191||D==198||D==208||215<=D&&D<=216||222<=D&&D<=225||D==230||232<=D&&D<=234||236<=D&&D<=237||D==240||242<=D&&D<=243||247<=D&&D<=250||D==252||D==254||D==257||D==273||D==275||D==283||294<=D&&D<=295||D==299||305<=D&&D<=307||D==312||319<=D&&D<=322||D==324||328<=D&&D<=331||D==333||338<=D&&D<=339||358<=D&&D<=359||D==363||D==462||D==464||D==466||D==468||D==470||D==472||D==474||D==476||D==593||D==609||D==708||D==711||713<=D&&D<=715||D==717||D==720||728<=D&&D<=731||D==733||D==735||768<=D&&D<=879||913<=D&&D<=929||931<=D&&D<=937||945<=D&&D<=961||963<=D&&D<=969||D==1025||1040<=D&&D<=1103||D==1105||D==8208||8211<=D&&D<=8214||8216<=D&&D<=8217||8220<=D&&D<=8221||8224<=D&&D<=8226||8228<=D&&D<=8231||D==8240||8242<=D&&D<=8243||D==8245||D==8251||D==8254||D==8308||D==8319||8321<=D&&D<=8324||D==8364||D==8451||D==8453||D==8457||D==8467||D==8470||8481<=D&&D<=8482||D==8486||D==8491||8531<=D&&D<=8532||8539<=D&&D<=8542||8544<=D&&D<=8555||8560<=D&&D<=8569||D==8585||8592<=D&&D<=8601||8632<=D&&D<=8633||D==8658||D==8660||D==8679||D==8704||8706<=D&&D<=8707||8711<=D&&D<=8712||D==8715||D==8719||D==8721||D==8725||D==8730||8733<=D&&D<=8736||D==8739||D==8741||8743<=D&&D<=8748||D==8750||8756<=D&&D<=8759||8764<=D&&D<=8765||D==8776||D==8780||D==8786||8800<=D&&D<=8801||8804<=D&&D<=8807||8810<=D&&D<=8811||8814<=D&&D<=8815||8834<=D&&D<=8835||8838<=D&&D<=8839||D==8853||D==8857||D==8869||D==8895||D==8978||9312<=D&&D<=9449||9451<=D&&D<=9547||9552<=D&&D<=9587||9600<=D&&D<=9615||9618<=D&&D<=9621||9632<=D&&D<=9633||9635<=D&&D<=9641||9650<=D&&D<=9651||9654<=D&&D<=9655||9660<=D&&D<=9661||9664<=D&&D<=9665||9670<=D&&D<=9672||D==9675||9678<=D&&D<=9681||9698<=D&&D<=9701||D==9711||9733<=D&&D<=9734||D==9737||9742<=D&&D<=9743||9748<=D&&D<=9749||D==9756||D==9758||D==9792||D==9794||9824<=D&&D<=9825||9827<=D&&D<=9829||9831<=D&&D<=9834||9836<=D&&D<=9837||D==9839||9886<=D&&D<=9887||9918<=D&&D<=9919||9924<=D&&D<=9933||9935<=D&&D<=9953||D==9955||9960<=D&&D<=9983||D==10045||D==10071||10102<=D&&D<=10111||11093<=D&&D<=11097||12872<=D&&D<=12879||57344<=D&&D<=63743||65024<=D&&D<=65039||D==65533||127232<=D&&D<=127242||127248<=D&&D<=127277||127280<=D&&D<=127337||127344<=D&&D<=127386||917760<=D&&D<=917999||983040<=D&&D<=1048573||1048576<=D&&D<=1114109?"A":"N"},t.characterLength=function(E){var B=this.eastAsianWidth(E);return B=="F"||B=="W"||B=="A"?2:1};function F(E){return E.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[^\uD800-\uDFFF]/g)||[]}t.length=function(E){for(var B=F(E),A=0,D=0;D<B.length;D++)A=A+this.characterLength(B[D]);return A},t.slice=function(E,B,A){textLen=t.length(E),B=B||0,A=A||1,B<0&&(B=textLen+B),A<0&&(A=textLen+A);for(var D="",f=0,y=F(E),d=0;d<y.length;d++){var _=y[d],w=t.length(_);if(f>=B-(w==2?1:0))if(f+w<=A)D+=_;else break;f+=w}return D}})(eastasianwidth);var eastasianwidthExports=eastasianwidth.exports;const eastAsianWidth=getDefaultExportFromCjs(eastasianwidthExports);var emojiRegex=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g};const emojiRegex$1=getDefaultExportFromCjs(emojiRegex);function stringWidth(C,t={}){if(typeof C!="string"||C.length===0||(t={ambiguousIsNarrow:!0,...t},C=stripAnsi(C),C.length===0))return 0;C=C.replace(emojiRegex$1(),"  ");const F=t.ambiguousIsNarrow?1:2;let E=0;for(const B of C){const A=B.codePointAt(0);if(A<=31||A>=127&&A<=159||A>=768&&A<=879)continue;switch(eastAsianWidth.eastAsianWidth(B)){case"F":case"W":E+=2;break;case"A":E+=F;break;default:E+=1}}return E}const ANSI_BACKGROUND_OFFSET=10,wrapAnsi16=(C=0)=>t=>`\x1B[${t+C}m`,wrapAnsi256=(C=0)=>t=>`\x1B[${38+C};5;${t}m`,wrapAnsi16m=(C=0)=>(t,F,E)=>`\x1B[${38+C};2;${t};${F};${E}m`,styles={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};Object.keys(styles.modifier);const foregroundColorNames=Object.keys(styles.color),backgroundColorNames=Object.keys(styles.bgColor);[...foregroundColorNames,...backgroundColorNames];function assembleStyles(){const C=new Map;for(const[t,F]of Object.entries(styles)){for(const[E,B]of Object.entries(F))styles[E]={open:`\x1B[${B[0]}m`,close:`\x1B[${B[1]}m`},F[E]=styles[E],C.set(B[0],B[1]);Object.defineProperty(styles,t,{value:F,enumerable:!1})}return Object.defineProperty(styles,"codes",{value:C,enumerable:!1}),styles.color.close="\x1B[39m",styles.bgColor.close="\x1B[49m",styles.color.ansi=wrapAnsi16(),styles.color.ansi256=wrapAnsi256(),styles.color.ansi16m=wrapAnsi16m(),styles.bgColor.ansi=wrapAnsi16(ANSI_BACKGROUND_OFFSET),styles.bgColor.ansi256=wrapAnsi256(ANSI_BACKGROUND_OFFSET),styles.bgColor.ansi16m=wrapAnsi16m(ANSI_BACKGROUND_OFFSET),Object.defineProperties(styles,{rgbToAnsi256:{value:(t,F,E)=>t===F&&F===E?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(F/255*5)+Math.round(E/255*5),enumerable:!1},hexToRgb:{value:t=>{const F=/[a-f\d]{6}|[a-f\d]{3}/i.exec(t.toString(16));if(!F)return[0,0,0];let[E]=F;E.length===3&&(E=[...E].map(A=>A+A).join(""));const B=Number.parseInt(E,16);return[B>>16&255,B>>8&255,B&255]},enumerable:!1},hexToAnsi256:{value:t=>styles.rgbToAnsi256(...styles.hexToRgb(t)),enumerable:!1},ansi256ToAnsi:{value:t=>{if(t<8)return 30+t;if(t<16)return 90+(t-8);let F,E,B;if(t>=232)F=((t-232)*10+8)/255,E=F,B=F;else{t-=16;const f=t%36;F=Math.floor(t/36)/5,E=Math.floor(f/6)/5,B=f%6/5}const A=Math.max(F,E,B)*2;if(A===0)return 30;let D=30+(Math.round(B)<<2|Math.round(E)<<1|Math.round(F));return A===2&&(D+=60),D},enumerable:!1},rgbToAnsi:{value:(t,F,E)=>styles.ansi256ToAnsi(styles.rgbToAnsi256(t,F,E)),enumerable:!1},hexToAnsi:{value:t=>styles.ansi256ToAnsi(styles.hexToAnsi256(t)),enumerable:!1}}),styles}const ansiStyles=assembleStyles(),ESCAPES=new Set(["\x1B","\x9B"]),END_CODE=39,ANSI_ESCAPE_BELL="\x07",ANSI_CSI="[",ANSI_OSC="]",ANSI_SGR_TERMINATOR="m",ANSI_ESCAPE_LINK=`${ANSI_OSC}8;;`,wrapAnsiCode=C=>`${ESCAPES.values().next().value}${ANSI_CSI}${C}${ANSI_SGR_TERMINATOR}`,wrapAnsiHyperlink=C=>`${ESCAPES.values().next().value}${ANSI_ESCAPE_LINK}${C}${ANSI_ESCAPE_BELL}`,wordLengths=C=>C.split(" ").map(t=>stringWidth(t)),wrapWord=(C,t,F)=>{const E=[...t];let B=!1,A=!1,D=stringWidth(stripAnsi(C[C.length-1]));for(const[f,y]of E.entries()){const d=stringWidth(y);if(D+d<=F?C[C.length-1]+=y:(C.push(y),D=0),ESCAPES.has(y)&&(B=!0,A=E.slice(f+1).join("").startsWith(ANSI_ESCAPE_LINK)),B){A?y===ANSI_ESCAPE_BELL&&(B=!1,A=!1):y===ANSI_SGR_TERMINATOR&&(B=!1);continue}D+=d,D===F&&f<E.length-1&&(C.push(""),D=0)}!D&&C[C.length-1].length>0&&C.length>1&&(C[C.length-2]+=C.pop())},stringVisibleTrimSpacesRight=C=>{const t=C.split(" ");let F=t.length;for(;F>0&&!(stringWidth(t[F-1])>0);)F--;return F===t.length?C:t.slice(0,F).join(" ")+t.slice(F).join("")},exec=(C,t,F={})=>{if(F.trim!==!1&&C.trim()==="")return"";let E="",B,A;const D=wordLengths(C);let f=[""];for(const[d,_]of C.split(" ").entries()){F.trim!==!1&&(f[f.length-1]=f[f.length-1].trimStart());let w=stringWidth(f[f.length-1]);if(d!==0&&(w>=t&&(F.wordWrap===!1||F.trim===!1)&&(f.push(""),w=0),(w>0||F.trim===!1)&&(f[f.length-1]+=" ",w++)),F.hard&&D[d]>t){const S=t-w,$=1+Math.floor((D[d]-S-1)/t);Math.floor((D[d]-1)/t)<$&&f.push(""),wrapWord(f,_,t);continue}if(w+D[d]>t&&w>0&&D[d]>0){if(F.wordWrap===!1&&w<t){wrapWord(f,_,t);continue}f.push("")}if(w+D[d]>t&&F.wordWrap===!1){wrapWord(f,_,t);continue}f[f.length-1]+=_}F.trim!==!1&&(f=f.map(d=>stringVisibleTrimSpacesRight(d)));const y=[...f.join(`
`)];for(const[d,_]of y.entries()){if(E+=_,ESCAPES.has(_)){const{groups:S}=new RegExp(`(?:\\${ANSI_CSI}(?<code>\\d+)m|\\${ANSI_ESCAPE_LINK}(?<uri>.*)${ANSI_ESCAPE_BELL})`).exec(y.slice(d).join(""))||{groups:{}};if(S.code!==void 0){const $=Number.parseFloat(S.code);B=$===END_CODE?void 0:$}else S.uri!==void 0&&(A=S.uri.length===0?void 0:S.uri)}const w=ansiStyles.codes.get(Number(B));y[d+1]===`
`?(A&&(E+=wrapAnsiHyperlink("")),B&&w&&(E+=wrapAnsiCode(w))):_===`
`&&(B&&w&&(E+=wrapAnsiCode(B)),A&&(E+=wrapAnsiHyperlink(A)))}return E};function wrapAnsi(C,t,F){return String(C).normalize().replace(/\r\n/g,`
`).split(`
`).map(E=>exec(E,t,F)).join(`
`)}const a$1=["up","down","left","right","space","enter","cancel"],settings={actions:new Set(a$1),aliases:new Map([["k","up"],["j","down"],["h","left"],["l","right"],["","cancel"],["escape","cancel"]])};function updateSettings(C){for(const t in C){const F=t;if(!Object.hasOwn(C,F))continue;const E=C[F];switch(F){case"aliases":{for(const B in E)Object.hasOwn(E,B)&&(settings.aliases.has(B)||settings.aliases.set(B,E[B]));break}}}}function isActionKey(C,t){if(typeof C=="string")return settings.aliases.get(C)===t;for(const F of C)if(F!==void 0&&isActionKey(F,t))return!0;return!1}function diffLines(C,t){if(C===t)return;const F=C.split(`
`),E=t.split(`
`),B=[];for(let A=0;A<Math.max(F.length,E.length);A++)F[A]!==E[A]&&B.push(A);return B}const x=globalThis.process.platform.startsWith("win"),CANCEL_SYMBOL=Symbol("clack:cancel");function isCancel(C){return C===CANCEL_SYMBOL}function setRawMode(C,t){const F=C;F.isTTY&&F.setRawMode(t)}function block({input:C=node_process.stdin,output:t=node_process.stdout,overwrite:F=!0,hideCursor:E=!0}={}){const B=s__namespace.createInterface({input:C,output:t,prompt:"",tabSize:1});s__namespace.emitKeypressEvents(C,B),C.isTTY&&C.setRawMode(!0);const A=(D,{name:f,sequence:y})=>{const d=String(D);if(isActionKey([d,f,y],"cancel")){E&&t.write(sisteransi.cursor.show),process.exit(0);return}if(!F)return;const _=f==="return"?0:-1,w=f==="return"?-1:0;s__namespace.moveCursor(t,_,w,()=>{s__namespace.clearLine(t,1,()=>{C.once("keypress",A)})})};return E&&t.write(sisteransi.cursor.hide),C.once("keypress",A),()=>{C.off("keypress",A),E&&t.write(sisteransi.cursor.show),C.isTTY&&!x&&C.setRawMode(!1),B.terminal=!1,B.close()}}var b=Object.defineProperty,v$1=(C,t,F)=>t in C?b(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,s$1=(C,t,F)=>(v$1(C,typeof t!="symbol"?t+"":t,F),F);class k{constructor(t,F=!0){s$1(this,"input"),s$1(this,"output"),s$1(this,"_abortSignal"),s$1(this,"rl"),s$1(this,"opts"),s$1(this,"_render"),s$1(this,"_track",!1),s$1(this,"_prevFrame",""),s$1(this,"_subscribers",new Map),s$1(this,"_cursor",0),s$1(this,"state","initial"),s$1(this,"error",""),s$1(this,"value");const{input:E=node_process.stdin,output:B=node_process.stdout,render:A,signal:D,...f}=t;this.opts=f,this.onKeypress=this.onKeypress.bind(this),this.close=this.close.bind(this),this.render=this.render.bind(this),this._render=A.bind(this),this._track=F,this._abortSignal=D,this.input=E,this.output=B}unsubscribe(){this._subscribers.clear()}setSubscriber(t,F){const E=this._subscribers.get(t)??[];E.push(F),this._subscribers.set(t,E)}on(t,F){this.setSubscriber(t,{cb:F})}once(t,F){this.setSubscriber(t,{cb:F,once:!0})}emit(t,...F){const E=this._subscribers.get(t)??[],B=[];for(const A of E)A.cb(...F),A.once&&B.push(()=>E.splice(E.indexOf(A),1));for(const A of B)A()}prompt(){return new Promise((t,F)=>{if(this._abortSignal){if(this._abortSignal.aborted)return this.state="cancel",this.close(),t(CANCEL_SYMBOL);this._abortSignal.addEventListener("abort",()=>{this.state="cancel",this.close()},{once:!0})}const E=new node_stream.Writable;E._write=(B,A,D)=>{this._track&&(this.value=this.rl?.line.replace(/\t/g,""),this._cursor=this.rl?.cursor??0,this.emit("value",this.value)),D()},this.input.pipe(E),this.rl=s__default.createInterface({input:this.input,output:E,tabSize:2,prompt:"",escapeCodeTimeout:50,terminal:!0}),s__default.emitKeypressEvents(this.input,this.rl),this.rl.prompt(),this.opts.initialValue!==void 0&&this._track&&this.rl.write(this.opts.initialValue),this.input.on("keypress",this.onKeypress),setRawMode(this.input,!0),this.output.on("resize",this.render),this.render(),this.once("submit",()=>{this.output.write(sisteransi.cursor.show),this.output.off("resize",this.render),setRawMode(this.input,!1),t(this.value)}),this.once("cancel",()=>{this.output.write(sisteransi.cursor.show),this.output.off("resize",this.render),setRawMode(this.input,!1),t(CANCEL_SYMBOL)})})}onKeypress(t,F){if(this.state==="error"&&(this.state="active"),F?.name&&(!this._track&&settings.aliases.has(F.name)&&this.emit("cursor",settings.aliases.get(F.name)),settings.actions.has(F.name)&&this.emit("cursor",F.name)),t&&(t.toLowerCase()==="y"||t.toLowerCase()==="n")&&this.emit("confirm",t.toLowerCase()==="y"),t==="	"&&this.opts.placeholder&&(this.value||(this.rl?.write(this.opts.placeholder),this.emit("value",this.opts.placeholder))),t&&this.emit("key",t.toLowerCase()),F?.name==="return"){if(this.opts.validate){const E=this.opts.validate(this.value);E&&(this.error=E instanceof Error?E.message:E,this.state="error",this.rl?.write(this.value))}this.state!=="error"&&(this.state="submit")}isActionKey([t,F?.name,F?.sequence],"cancel")&&(this.state="cancel"),(this.state==="submit"||this.state==="cancel")&&this.emit("finalize"),this.render(),(this.state==="submit"||this.state==="cancel")&&this.close()}close(){this.input.unpipe(),this.input.removeListener("keypress",this.onKeypress),this.output.write(`
`),setRawMode(this.input,!1),this.rl?.close(),this.rl=void 0,this.emit(`${this.state}`,this.value),this.unsubscribe()}restoreCursor(){const t=wrapAnsi(this._prevFrame,process.stdout.columns,{hard:!0}).split(`
`).length-1;this.output.write(sisteransi.cursor.move(-999,t*-1))}render(){const t=wrapAnsi(this._render(this)??"",process.stdout.columns,{hard:!0});if(t!==this._prevFrame){if(this.state==="initial")this.output.write(sisteransi.cursor.hide);else{const F=diffLines(this._prevFrame,t);if(this.restoreCursor(),F&&F?.length===1){const E=F[0];this.output.write(sisteransi.cursor.move(0,E)),this.output.write(sisteransi.erase.lines(1));const B=t.split(`
`);this.output.write(B[E]),this._prevFrame=t,this.output.write(sisteransi.cursor.move(0,B.length-E-1));return}if(F&&F?.length>1){const E=F[0];this.output.write(sisteransi.cursor.move(0,E)),this.output.write(sisteransi.erase.down());const B=t.split(`
`).slice(E);this.output.write(B.join(`
`)),this._prevFrame=t;return}this.output.write(sisteransi.erase.down())}this.output.write(t),this.state==="initial"&&(this.state="active"),this._prevFrame=t}}}class s extends k{get cursor(){return this.value?0:1}get _value(){return this.cursor===0}constructor(t){super(t,!1),this.value=!!t.initialValue,this.on("value",()=>{this.value=this._value}),this.on("confirm",F=>{this.output.write(sisteransi.cursor.move(0,-1)),this.value=F,this.state="submit",this.close()}),this.on("cursor",()=>{this.value=!this.value})}}var v=Object.defineProperty,g=(C,t,F)=>t in C?v(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,n$2=(C,t,F)=>(g(C,typeof t!="symbol"?t+"":t,F),F),c$1=(C,t,F)=>{if(!t.has(C))throw TypeError("Cannot "+F)},l$2=(C,t,F)=>(c$1(C,t,"read from private field"),F?F.call(C):t.get(C)),h=(C,t,F)=>{if(t.has(C))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(C):t.set(C,F)},p$1=(C,t,F,E)=>(c$1(C,t,"write to private field"),E?E.call(C,F):t.set(C,F),F),u$3;let m$1=class extends k{constructor(t){super(t,!1),n$2(this,"options"),n$2(this,"cursor",0),h(this,u$3,void 0);const{options:F}=t;p$1(this,u$3,t.selectableGroups!==!1),this.options=Object.entries(F).flatMap(([E,B])=>[{value:E,group:!0,label:E},...B.map(A=>({...A,group:E}))]),this.value=[...t.initialValues??[]],this.cursor=Math.max(this.options.findIndex(({value:E})=>E===t.cursorAt),l$2(this,u$3)?0:1),this.on("cursor",E=>{switch(E){case"left":case"up":{this.cursor=this.cursor===0?this.options.length-1:this.cursor-1;const B=this.options[this.cursor]?.group===!0;!l$2(this,u$3)&&B&&(this.cursor=this.cursor===0?this.options.length-1:this.cursor-1);break}case"down":case"right":{this.cursor=this.cursor===this.options.length-1?0:this.cursor+1;const B=this.options[this.cursor]?.group===!0;!l$2(this,u$3)&&B&&(this.cursor=this.cursor===this.options.length-1?0:this.cursor+1);break}case"space":this.toggleValue();break}})}getGroupItems(t){return this.options.filter(F=>F.group===t)}isGroupSelected(t){return this.getGroupItems(t).every(F=>this.value.includes(F.value))}toggleValue(){const t=this.options[this.cursor];if(t.group===!0){const F=t.value,E=this.getGroupItems(F);this.isGroupSelected(F)?this.value=this.value.filter(B=>E.findIndex(A=>A.value===B)===-1):this.value=[...this.value,...E.map(B=>B.value)],this.value=Array.from(new Set(this.value))}else{const F=this.value.includes(t.value);this.value=F?this.value.filter(E=>E!==t.value):[...this.value,t.value]}}};u$3=new WeakMap;var o$2=Object.defineProperty,a=(C,t,F)=>t in C?o$2(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,l$1=(C,t,F)=>(a(C,typeof t!="symbol"?t+"":t,F),F);let u$2=class extends k{constructor(t){super(t,!1),l$1(this,"options"),l$1(this,"cursor",0),this.options=t.options,this.value=[...t.initialValues??[]],this.cursor=Math.max(this.options.findIndex(({value:F})=>F===t.cursorAt),0),this.on("key",F=>{F==="a"&&this.toggleAll()}),this.on("cursor",F=>{switch(F){case"left":case"up":this.cursor=this.cursor===0?this.options.length-1:this.cursor-1;break;case"down":case"right":this.cursor=this.cursor===this.options.length-1?0:this.cursor+1;break;case"space":this.toggleValue();break}})}get _value(){return this.options[this.cursor].value}toggleAll(){const t=this.value.length===this.options.length;this.value=t?[]:this.options.map(F=>F.value)}toggleValue(){const t=this.value.includes(this._value);this.value=t?this.value.filter(F=>F!==this._value):[...this.value,this._value]}};var u$1=Object.defineProperty,n$1=(C,t,F)=>t in C?u$1(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,e=(C,t,F)=>(n$1(C,typeof t!="symbol"?t+"":t,F),F);class m extends k{constructor({mask:t,...F}){super(F),e(this,"valueWithCursor",""),e(this,"_mask","\u2022"),this._mask=t??"\u2022",this.on("finalize",()=>{this.valueWithCursor=this.masked}),this.on("value",()=>{if(this.cursor>=this.value.length)this.valueWithCursor=`${this.masked}${i__default.inverse(i__default.hidden("_"))}`;else{const E=this.masked.slice(0,this.cursor),B=this.masked.slice(this.cursor);this.valueWithCursor=`${E}${i__default.inverse(B[0])}${B.slice(1)}`}})}get cursor(){return this._cursor}get masked(){return this.value.replaceAll(/./g,this._mask)}}var o$1=Object.defineProperty,n=(C,t,F)=>t in C?o$1(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,r=(C,t,F)=>(n(C,typeof t!="symbol"?t+"":t,F),F);class u extends k{constructor(t){super(t,!1),r(this,"options"),r(this,"cursor",0),this.options=t.options,this.cursor=this.options.findIndex(({value:F})=>F===t.initialValue),this.cursor===-1&&(this.cursor=0),this.changeValue(),this.on("cursor",F=>{switch(F){case"left":case"up":this.cursor=this.cursor===0?this.options.length-1:this.cursor-1;break;case"down":case"right":this.cursor=this.cursor===this.options.length-1?0:this.cursor+1;break}this.changeValue()})}get _value(){return this.options[this.cursor]}changeValue(){this.value=this._value.value}}var p=Object.defineProperty,l=(C,t,F)=>t in C?p(C,t,{enumerable:!0,configurable:!0,writable:!0,value:F}):C[t]=F,i=(C,t,F)=>(l(C,typeof t!="symbol"?t+"":t,F),F);class c extends k{constructor(t){super(t,!1),i(this,"options"),i(this,"cursor",0),this.options=t.options;const F=this.options.map(({value:[E]})=>E?.toLowerCase());this.cursor=Math.max(F.indexOf(t.initialValue),0),this.on("key",E=>{if(!F.includes(E))return;const B=this.options.find(({value:[A]})=>A?.toLowerCase()===E);B&&(this.value=B.value,this.state="submit",this.emit("submit"))})}}class o extends k{get valueWithCursor(){if(this.state==="submit")return this.value;if(this.cursor>=this.value.length)return`${this.value}\u2588`;const t=this.value.slice(0,this.cursor),[F,...E]=this.value.slice(this.cursor);return`${t}${i__default.inverse(F)}${E.join("")}`}get cursor(){return this._cursor}constructor(t){super(t),this.on("finalize",()=>{this.value||(this.value=t.defaultValue)})}}exports.ConfirmPrompt=s,exports.GroupMultiSelectPrompt=m$1,exports.MultiSelectPrompt=u$2,exports.PasswordPrompt=m,exports.Prompt=k,exports.SelectKeyPrompt=c,exports.SelectPrompt=u,exports.TextPrompt=o,exports.block=block,exports.isCancel=isCancel,exports.updateSettings=updateSettings;
//# sourceMappingURL=index.cjs.map
