const https = require('https');

const API_KEY = 'd9b0d31e-41a2-47b2-8218-08e5830c2f0c';
const BASE_URL = 'https://api-qa.sling-dev.com';

function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'api-qa.sling-dev.com',
            port: 443,
            path: path,
            method: method,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY
            }
        };

        if (data) {
            const jsonData = JSON.stringify(data);
            options.headers['Content-Length'] = Buffer.byteLength(jsonData);
        }

        const req = https.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(responseData);
                    resolve({
                        statusCode: res.statusCode,
                        data: parsedData
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        data: responseData
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function testAPI() {
    console.log('Testing Kylas API endpoints...\n');

    try {
        // Test 1: Get system fields for leads
        console.log('1. Testing GET /v1/layouts/leads/system-fields?view=create');
        const fieldsResponse = await makeRequest('GET', '/v1/layouts/leads/system-fields?view=create');
        console.log('Status:', fieldsResponse.statusCode);
        if (fieldsResponse.statusCode === 200) {
            console.log('✅ System fields retrieved successfully');
            console.log('Number of fields:', JSON.parse(fieldsResponse.data.data || '[]').length);
        } else {
            console.log('❌ Failed to get system fields');
            console.log('Response:', fieldsResponse.data);
        }

        // Test 2: Create a lead
        console.log('\n2. Testing POST /v1/leads (Create Lead)');
        const createData = {
            firstName: "Test",
            lastName: "Lead",
            address: "Test Address",
            city: "Test City",
            customFieldValues: {
                myTest: "test value"
            }
        };
        
        const createResponse = await makeRequest('POST', '/v1/leads', createData);
        console.log('Status:', createResponse.statusCode);
        if (createResponse.statusCode === 200 || createResponse.statusCode === 201) {
            console.log('✅ Lead created successfully');
            console.log('Lead ID:', createResponse.data.id);
            
            // Test 3: Get the created lead
            if (createResponse.data.id) {
                console.log('\n3. Testing GET /v1/leads/{id} (Get Lead)');
                const getResponse = await makeRequest('GET', `/v1/leads/${createResponse.data.id}`);
                console.log('Status:', getResponse.statusCode);
                if (getResponse.statusCode === 200) {
                    console.log('✅ Lead retrieved successfully');
                    console.log('Lead name:', `${getResponse.data.firstName} ${getResponse.data.lastName}`);
                } else {
                    console.log('❌ Failed to get lead');
                    console.log('Response:', getResponse.data);
                }

                // Test 4: Update the lead
                console.log('\n4. Testing PUT /v1/leads/{id} (Update Lead)');
                const updateData = {
                    firstName: "Updated Test",
                    lastName: "Updated Lead",
                    customFieldValues: {
                        myTest: "updated test value"
                    }
                };
                
                const updateResponse = await makeRequest('PUT', `/v1/leads/${createResponse.data.id}`, updateData);
                console.log('Status:', updateResponse.statusCode);
                if (updateResponse.statusCode === 200) {
                    console.log('✅ Lead updated successfully');
                    console.log('Updated name:', `${updateResponse.data.firstName} ${updateResponse.data.lastName}`);
                } else {
                    console.log('❌ Failed to update lead');
                    console.log('Response:', updateResponse.data);
                }
            }
        } else {
            console.log('❌ Failed to create lead');
            console.log('Response:', createResponse.data);
        }

        // Test 5: Get many leads
        console.log('\n5. Testing GET /v1/leads (Get Many Leads)');
        const getManyResponse = await makeRequest('GET', '/v1/leads?limit=5');
        console.log('Status:', getManyResponse.statusCode);
        if (getManyResponse.statusCode === 200) {
            console.log('✅ Leads retrieved successfully');
            console.log('Number of leads:', getManyResponse.data.length || 0);
        } else {
            console.log('❌ Failed to get leads');
            console.log('Response:', getManyResponse.data);
        }

    } catch (error) {
        console.error('Error testing API:', error);
    }
}

testAPI();
